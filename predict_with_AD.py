#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
机器学习回归模型预测与应用域判断整合脚本

功能:
1. 使用训练好的机器学习模型进行回归预测（支持PubChem指纹）
2. 使用预设的最佳参数判断是否在应用域内
3. SHAP可解释性分析
4. 类似物筛查功能
5. 输出整合结果

使用方法:
1. 在下面的配置区域设置你的参数
2. 运行: python predict_with_AD.py
"""

import pandas as pd
import numpy as np
import os
import pickle
from rdkit import Chem
from rdkit.Chem import DataStructs, AllChem, Draw
import warnings
warnings.filterwarnings('ignore')

# 追加：命令行与工具
import argparse
from pathlib import Path
from datetime import datetime
import random

# 绘图：使用非交互后端，避免服务器/命令行报错
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt

# 导入应用域模块
from adsal import NSG

# =============================================================================
# 配置参数 - 请在这里修改你的设置
# =============================================================================

# 输出文件配置
OUTPUT_FILE = 'prediction_with_AD_results.xlsx'  # 输出文件名

# 最佳应用域参数
OPTIMAL_DENSLB = 1e-10    # 最佳相似性密度阈值
OPTIMAL_LDUB = 0.9       # 最佳局域不连续性阈值

# 模型和数据路径
MODEL_PATH = 'saved_model/lightgbm_model_PubChem.pkl'  # 模型路径（支持多种模型类型和指纹）
TRAINING_DATA_PATH = 'TrainingSet_Regression.xlsx'  # 训练集数据路径

# =============================================================================
# 注意：
# 1. 请确保你已经训练并保存了模型（支持PubChem、Mordred、MACCS等指纹）
# 2. 将获得的最佳densLB和LdUB值填入上面的OPTIMAL_DENSLB和OPTIMAL_LDUB
# 3. 修改INPUT_FILE为你要分析的化合物文件路径
# 4. 本脚本支持PubChem指纹、SHAP分析和类似物筛查功能
# 5. 运行: python predict_with_AD.py [--input-file your_file.xlsx] [--shap]
# =============================================================================

# =============================================================================
# 预测功能类和函数
# =============================================================================

class LightGBMPredictor:
    """LightGBM回归预测器（支持PubChem指纹）"""

    def __init__(self, model_path):
        """初始化预测器"""
        self.model_path = model_path
        self._load_model()

    def _load_model(self):
        """加载模型和相关组件"""
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型文件不存在: {self.model_path}")

        print(f"加载模型: {self.model_path}")

        with open(self.model_path, 'rb') as f:
            model_components = pickle.load(f)

        self.model = model_components['model']
        self.scaler = model_components['scaler']
        self.max_length = model_components['max_length']
        self.fingerprint_column = model_components.get('fingerprint_column', 'PubChem')
        print(f"模型加载成功")
        print(f"使用PubChem指纹进行预测")

    def _validate_smiles(self, smiles):
        """验证SMILES字符串"""
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                return False, "无效的SMILES字符串"

            canonical_smiles = Chem.MolToSmiles(mol, isomericSmiles=True)
            return True, canonical_smiles
        except Exception as e:
            return False, f"SMILES处理错误: {str(e)}"

    def _generate_fingerprint(self, smiles):
        """生成PubChem指纹（用于预测）"""
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                return []

            # 使用PubChem指纹
            from skfp.fingerprints import PubChemFingerprint
            pubchem_fp = PubChemFingerprint()
            fp_array = pubchem_fp.transform([smiles])[0]
            return fp_array.tolist()

        except Exception as e:
            print(f"PubChem指纹生成错误: {e}")
            return []

    def _preprocess_single_sample(self, smiles):
        """预处理单个样本（仅使用指纹特征）"""
        # 生成指纹
        fingerprint = self._generate_fingerprint(smiles)

        if not fingerprint:
            raise ValueError("指纹生成失败")

        # 将指纹转换为numpy数组
        X = np.array(fingerprint).reshape(1, -1)

        # 确保指纹长度与训练时一致
        if hasattr(self, 'max_length') and X.shape[1] != self.max_length:
            # 如果长度不匹配，进行填充或截断
            if X.shape[1] < self.max_length:
                # 填充0
                padding = np.zeros((1, self.max_length - X.shape[1]))
                X = np.hstack([X, padding])
            else:
                # 截断
                X = X[:, :self.max_length]

        # 特征缩放
        X_scaled = self.scaler.transform(X)

        return X_scaled

    def predict_single(self, smiles):
        """预测单个SMILES"""
        is_valid, result = self._validate_smiles(smiles)
        if not is_valid:
            return {'smiles': smiles, 'error': result}

        canonical_smiles = result

        try:
            # 预处理
            X_scaled = self._preprocess_single_sample(canonical_smiles)

            # 预测 (负对数形式)
            log_prediction = self.model.predict(X_scaled)[0]

            # 转换为原数值 (10^(-log_prediction))
            original_value = 10**(-log_prediction)

            return {
                'smiles': smiles,
                'canonical_smiles': canonical_smiles,
                'prediction_log': float(log_prediction),  # 负对数预测值
                'prediction': float(original_value),      # 原数值预测值
                'error': None
            }

        except Exception as e:
            return {'smiles': smiles, 'error': f"预测错误: {str(e)}"}

    def predict_batch(self, smiles_list):
        """
        批量预测SMILES列表

        Args:
            smiles_list: SMILES字符串列表

        Returns:
            预测结果列表，每个元素包含：
            - smiles: 原始SMILES
            - canonical_smiles: 规范化SMILES
            - prediction: 预测值
            - error: 错误信息 (如果没有错误则为None)
        """
        results = []

        print(f"批量预测 {len(smiles_list)} 个化合物...")

        for i, smiles in enumerate(smiles_list):
            if i % 10 == 0 and i > 0:
                print(f"已处理 {i}/{len(smiles_list)} 个化合物...")

            result = self.predict_single(smiles)
            results.append(result)

        print(f"批量预测完成！")
        return results

def predict_on_input_file(input_file_path, model_path, output_file_path=None):
    """
    对输入文件进行预测

    Args:
        input_file_path: 输入文件路径（Excel格式）
        model_path: 模型文件路径
        output_file_path: 输出文件路径，如果为None则在原文件名后加_predicted
    """
    print("开始对输入文件进行LightGBM回归预测...")

    # 1. 检查输入文件
    if not os.path.exists(input_file_path):
        print(f"输入文件不存在: {input_file_path}")
        return

    # 2. 确定输出文件路径
    if output_file_path is None:
        base_name = os.path.splitext(input_file_path)[0]
        output_file_path = f"{base_name}_lightgbm_predicted.xlsx"

    print(f"输入文件: {input_file_path}")
    print(f"模型文件: {model_path}")
    print(f"输出文件: {output_file_path}")

    # 3. 加载预测器
    try:
        predictor = LightGBMPredictor(model_path)
    except Exception as e:
        print(f"模型加载失败: {str(e)}")
        return

    # 4. 加载输入数据
    try:
        df = pd.read_excel(input_file_path)
        print(f"样本数量: {len(df)}")

        # 检查必要列
        if 'smiles' not in df.columns:
            print("输入数据缺少必要列: smiles")
            return

    except Exception as e:
        print(f"数据加载失败: {str(e)}")
        return

    # 5. 准备结果DataFrame
    result_df = df.copy()
    result_df['canonical_smiles'] = ''
    result_df['prediction_log'] = np.nan  # 负对数预测值
    result_df['prediction'] = np.nan      # 原数值预测值
    result_df['error'] = ''

    # 6. 进行预测
    print("开始预测...")
    for idx, row in df.iterrows():
        smiles = row['smiles']
        result = predictor.predict_single(smiles)

        if result['error']:
            result_df.loc[idx, 'error'] = result['error']
        else:
            result_df.loc[idx, 'canonical_smiles'] = result['canonical_smiles']
            result_df.loc[idx, 'prediction_log'] = result['prediction_log']  # 负对数值
            result_df.loc[idx, 'prediction'] = result['prediction']          # 原数值

    # 7. 保存结果
    try:
        result_df.to_excel(output_file_path, index=False)
        print(f"结果已保存到: {output_file_path}")

        # 显示统计结果
        valid_predictions = result_df[result_df['error'] == '']
        if len(valid_predictions) > 0:
            print(f"\n预测统计:")
            print(f"有效预测: {len(valid_predictions)}/{len(result_df)}")
            print(f"负对数预测值 - 平均: {valid_predictions['prediction_log'].mean():.4f}, 范围: {valid_predictions['prediction_log'].min():.4f} - {valid_predictions['prediction_log'].max():.4f}")
            print(f"原数值预测值 - 平均: {valid_predictions['prediction'].mean():.6f} mol/L, 范围: {valid_predictions['prediction'].min():.6f} - {valid_predictions['prediction'].max():.6f} mol/L")

        errors = result_df[result_df['error'] != '']
        if len(errors) > 0:
            print(f"无法预测样本: {len(errors)}")

    except Exception as e:
        print(f"保存结果失败: {str(e)}")

# exp权重函数
def expWt(x, a=15, eps=1e-6):
    """指数权重函数"""
    return np.exp(-a*(1-x)/(x + eps))

EXP_WEIGHT_PARAMS = {'a': 10}

# =============================================================================
# 核心功能函数
# =============================================================================

def load_training_data(file_path):
    """加载训练集数据"""
    print(f"加载训练集数据: {file_path}")
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"训练数据文件不存在: {file_path}")
    df = pd.read_excel(file_path)
    required_cols = ['smiles', 'y']
    target_col = 'y'
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"训练集缺少必要列: {missing_cols}")

    # 准备数据
    df_clean = df[required_cols].copy()
    if target_col != 'y':
        df_clean.rename(columns={target_col: 'y'}, inplace=True)  # 重命名以保持一致性
    df_clean.reset_index(drop=True, inplace=True)

    print(f"训练集样本数: {len(df_clean)}")
    print(f"使用目标变量列: {target_col}")
    return df_clean

def run_prediction(input_file, model_path, temp_output):
    """运行回归预测"""
    print("步骤1: 运行LightGBM回归预测")

    if not os.path.exists(model_path):
        raise FileNotFoundError(f"模型文件不存在: {model_path}")

    # 运行预测
    predict_on_input_file(input_file, model_path, temp_output)

    if not os.path.exists(temp_output):
        raise RuntimeError("预测失败，未生成预测文件")

    print("预测完成")
    return temp_output

def calculate_ad_metrics(df_train, df_query):
    """计算应用域指标"""
    print("步骤2: 计算应用域指标")

    # 创建NSG对象
    nsg = NSG(df_train, yCol='y', smiCol='smiles')

    # 计算分子指纹相似性
    nsg.calcPairwiseSimilarityWithFp('MACCS_keys')

    # 生成查询-训练相似性矩阵
    dfQTSM = nsg.genQTSM(df_query, 'smiles')

    # 计算应用域指标（使用exp权重函数）
    ad_metrics = nsg.queryADMetrics(
        dfQTSM,
        wtFunc1=expWt,
        kw1=EXP_WEIGHT_PARAMS,
        wtFunc2=expWt,
        kw2=EXP_WEIGHT_PARAMS,
        code='|exp'
    )

    # 合并结果
    df_result = df_query.join(ad_metrics)

    print("应用域指标计算完成")
    return df_result

def apply_ad_criteria(df_with_metrics, optimal_densLB, optimal_LdUB):
    """应用应用域判断标准"""
    print("步骤3: 应用域判断")

    print(f"相似性密度阈值 (densLB): {optimal_densLB}")
    print(f"局域不连续性阈值 (LdUB): {optimal_LdUB}")

    # 应用域判断条件
    ad_condition = (
        (df_with_metrics['simiDensity|exp'] >= optimal_densLB) &
        (df_with_metrics['simiWtLD_w|exp'] <= optimal_LdUB)
    )

    # 添加应用域判断结果
    df_result = df_with_metrics.copy()
    df_result['in_applicability_domain'] = ad_condition
    df_result['ad_densLB_threshold'] = optimal_densLB
    df_result['ad_LdUB_threshold'] = optimal_LdUB
    df_result['ad_density_value'] = df_with_metrics['simiDensity|exp']
    df_result['ad_ld_value'] = df_with_metrics['simiWtLD_w|exp']

    # 添加应用域判断原因
    def get_ad_reason(row):
        if row['in_applicability_domain']:
            return "在应用域内"
        else:
            reasons = []
            if row['ad_density_value'] < optimal_densLB:
                reasons.append(f"相似性密度({row['ad_density_value']:.3f}) < 阈值({optimal_densLB})")
            if row['ad_ld_value'] > optimal_LdUB:
                reasons.append(f"局域不连续性({row['ad_ld_value']:.3f}) > 阈值({optimal_LdUB})")
            return "; ".join(reasons)

    df_result['ad_reason'] = df_result.apply(get_ad_reason, axis=1)

    # 统计结果
    total_compounds = len(df_result)
    in_domain_count = df_result['in_applicability_domain'].sum()

    print(f"应用域判断结果:")
    print(f"总化合物数: {total_compounds}")
    print(f"应用域内: {in_domain_count} ({in_domain_count/total_compounds*100:.1f}%)")
    print(f"应用域外: {total_compounds - in_domain_count} ({(total_compounds - in_domain_count)/total_compounds*100:.1f}%)")

    return df_result

def generate_summary(df_result):
    """生成结果摘要"""
    print("\n结果摘要:")
    print("=" * 50)

    for idx, row in df_result.iterrows():
        compound_name = row.get('compound_name', f'化合物_{idx+1}')
        status = "应用域内" if row['in_applicability_domain'] else "应用域外"

        print(f"\n{compound_name}:")
        print(f"  SMILES: {row['smiles']}")

        # 显示两种预测值
        if 'prediction_log' in row and pd.notna(row['prediction_log']):
            print(f"  负对数预测值: {row['prediction_log']:.4f}")
            print(f"  原数值预测值: {row['prediction']:.6f} mol/L")
        else:
            print(f"  预测值: {row.get('prediction', 'N/A'):.4f}")

        print(f"  应用域状态: {status}")
        print(f"  相似性密度: {row['ad_density_value']:.4f}")
        print(f"  局域不连续性: {row['ad_ld_value']:.4f}")
        print(f"  判断原因: {row['ad_reason']}")

        # 给出建议
        if row['in_applicability_domain']:
            suggestion = "预测结果可信，建议采用"
        else:
            suggestion = "预测结果可信度较低，建议实验验证"
        print(f"  建议: {suggestion}")

    print("=" * 50)

# =============================================================================
# SHAP 集成函数
# =============================================================================

def build_feature_matrix_from_smiles(predictor, smiles_list):
    """
    基于 predictor 的单样本预处理，构建批量特征矩阵（缩放后）
    返回:
      X: np.ndarray [n_samples, n_features]
      ok_indices: 在输入 smiles_list 中成功构建特征的局部索引列表
    """
    print(f"SHAP: 为 {len(smiles_list)} 个化合物构建特征矩阵...")
    X_list = []
    ok_indices = []
    for i, s in enumerate(smiles_list):
        try:
            X_scaled = predictor._preprocess_single_sample(s)
            if X_scaled is not None and X_scaled.size > 0:
                X_list.append(X_scaled[0])
                ok_indices.append(i)
        except Exception:
            # 忽略个别失败样本
            continue
    if len(X_list) == 0:
        return np.empty((0, 0)), []
    X = np.vstack(X_list)
    print(f"SHAP: 成功构建了 {X.shape[0]} 个样本的特征矩阵，特征维度: {X.shape[1]}")
    return X, ok_indices

def get_generic_feature_names(n_features):
    """生成通用特征名 f0..f{n-1}"""
    return [f"f{i}" for i in range(n_features)]



def get_pubchem_feature_names():
    """获取PubChem指纹的真实特征名称"""
    try:
        from skfp.fingerprints import PubChemFingerprint
        pubchem_fp = PubChemFingerprint()
        # 获取PubChem指纹的特征名称
        feature_names = pubchem_fp.get_feature_names()
        return feature_names
    except ImportError:
        print("警告: skfp包未安装，无法获取PubChem特征名称")
        return None
    except AttributeError:
        print("警告: 当前skfp版本不支持get_feature_names方法")
        # 如果没有get_feature_names方法，使用已知的PubChem特征名称
        return get_known_pubchem_features()
    except Exception as e:
        print(f"警告: 获取PubChem特征名称失败: {e}")
        return None

def get_known_pubchem_features():
    """返回已知的PubChem指纹特征名称（前881位）"""
    # PubChem指纹的前881位特征的含义
    pubchem_features = [
        ">=1 any atom",
        ">=2 any atom",
        ">=3 any atom",
        ">=4 any atom",
        ">=5 any atom",
        ">=6 any atom",
        ">=7 any atom",
        ">=8 any atom",
        ">=9 any atom",
        ">=10 any atom",
        ">=11 any atom",
        ">=12 any atom",
        ">=13 any atom",
        ">=14 any atom",
        ">=15 any atom",
        ">=16 any atom",
        ">=17 any atom",
        ">=18 any atom",
        ">=19 any atom",
        ">=20 any atom",
        ">=21 any atom",
        ">=22 any atom",
        ">=23 any atom",
        ">=24 any atom",
        ">=25 any atom",
        ">=26 any atom",
        ">=27 any atom",
        ">=28 any atom",
        ">=29 any atom",
        ">=30 any atom",
        ">=31 any atom",
        ">=32 any atom",
        ">=1 saturated or aromatic carbon-only ring",
        ">=2 saturated or aromatic carbon-only ring",
        ">=3 saturated or aromatic carbon-only ring",
        ">=4 saturated or aromatic carbon-only ring",
        ">=5 saturated or aromatic carbon-only ring",
        ">=6 saturated or aromatic carbon-only ring",
        ">=7 saturated or aromatic carbon-only ring",
        ">=8 saturated or aromatic carbon-only ring",
        ">=9 saturated or aromatic carbon-only ring",
        ">=10 saturated or aromatic carbon-only ring",
        ">=1 saturated or aromatic nitrogen-containing ring",
        ">=2 saturated or aromatic nitrogen-containing ring",
        ">=3 saturated or aromatic nitrogen-containing ring",
        ">=4 saturated or aromatic nitrogen-containing ring",
        ">=5 saturated or aromatic nitrogen-containing ring",
        ">=6 saturated or aromatic nitrogen-containing ring",
        ">=1 saturated or aromatic heteroatom-containing ring",
        ">=2 saturated or aromatic heteroatom-containing ring",
        ">=3 saturated or aromatic heteroatom-containing ring",
        ">=4 saturated or aromatic heteroatom-containing ring",
        ">=5 saturated or aromatic heteroatom-containing ring",
        ">=6 saturated or aromatic heteroatom-containing ring",
        ">=7 saturated or aromatic heteroatom-containing ring",
        ">=8 saturated or aromatic heteroatom-containing ring",
        ">=9 saturated or aromatic heteroatom-containing ring",
        ">=10 saturated or aromatic heteroatom-containing ring",
        ">=1 unsaturated non-aromatic carbon-only ring",
        ">=2 unsaturated non-aromatic carbon-only ring",
        ">=1 unsaturated non-aromatic nitrogen-containing ring",
        ">=2 unsaturated non-aromatic nitrogen-containing ring",
        ">=1 unsaturated non-aromatic heteroatom-containing ring",
        ">=2 unsaturated non-aromatic heteroatom-containing ring",
        ">=1 aromatic ring",
        ">=2 aromatic ring",
        ">=3 aromatic ring",
        ">=4 aromatic ring",
        ">=5 aromatic ring",
        ">=1 heteroaromatic ring",
        ">=2 heteroaromatic ring",
        ">=3 heteroaromatic ring",
        ">=4 heteroaromatic ring",
        ">=1 ring size 3",
        ">=2 ring size 3",
        ">=1 ring size 4",
        ">=2 ring size 4",
        ">=1 ring size 5",
        ">=2 ring size 5",
        ">=3 ring size 5",
        ">=4 ring size 5",
        ">=1 ring size 6",
        ">=2 ring size 6",
        ">=3 ring size 6",
        ">=4 ring size 6",
        ">=5 ring size 6",
        ">=1 ring size 7",
        ">=2 ring size 7",
        ">=1 ring size 8",
        ">=2 ring size 8",
        ">=1 ring size 9",
        ">=2 ring size 9",
        ">=1 ring size 10+",
        ">=2 ring size 10+",
        ">=1 aromatic ring size 6",
        ">=2 aromatic ring size 6",
        ">=3 aromatic ring size 6",
        ">=4 aromatic ring size 6",
        ">=5 aromatic ring size 6",
        ">=1 aromatic ring size 5",
        ">=2 aromatic ring size 5",
        ">=3 aromatic ring size 5",
        ">=4 aromatic ring size 5",
        ">=5 aromatic ring size 5",
        ">=1 aromatic ring size 7",
        ">=2 aromatic ring size 7",
        ">=1 aromatic ring size 8",
        ">=2 aromatic ring size 8",
        ">=1 aromatic ring size 9",
        ">=2 aromatic ring size 9",
        ">=1 aromatic ring size 10+",
        ">=2 aromatic ring size 10+",
        # 这里只列出了前100个特征，实际PubChem有881个特征
        # 为了节省空间，后面的特征可以用通用名称补充
    ]

    # 如果需要更多特征，用通用名称补充
    while len(pubchem_features) < 881:
        idx = len(pubchem_features)
        pubchem_features.append(f"PubChem_feature_{idx}")

    return pubchem_features

def get_feature_names_from_model(n_features):
    """获取PubChem指纹的特征名称"""
    print("获取PubChem指纹特征名称...")
    pubchem_names = get_pubchem_feature_names()
    if pubchem_names:
        # 确保特征数量匹配
        if len(pubchem_names) >= n_features:
            return pubchem_names[:n_features]
        elif len(pubchem_names) < n_features:
            # 如果PubChem特征数量不足，补充通用名称
            extended_names = pubchem_names.copy()
            for i in range(len(pubchem_names), n_features):
                extended_names.append(f"Extra_PubChem_bit_{i}")
            return extended_names
    else:
        print("无法获取PubChem特征名称，使用通用名称")
        return [f"PubChem_bit_{i}" for i in range(n_features)]

def run_shap_explanation(df_with_ad, predictor, args, dataset_id, input_file=None):
    """
    执行 SHAP 解释：
    - 按 scope 过滤（all/ad）
    - 随机种子可复现的降采样
    - 解释器优先 TreeExplainer，失败回退 KernelExplainer
    - 保存数值、图像与元数据
    """
    try:
        import shap
    except Exception as e:
        print(f"未安装 shap 或导入失败: {e}. 可先运行 pip install shap 安装。")
        return

    # 过滤可解释样本
    df_base = df_with_ad.copy()
    if 'error' in df_base.columns:
        # 过滤掉有错误的样本（error列为空字符串或None表示无错误）
        df_base = df_base[(df_base['error'].isna()) | (df_base['error'] == '') | (df_base['error'].isnull())]
    if args.shap_scope == 'ad' and 'in_applicability_domain' in df_base.columns:
        df_base = df_base[df_base['in_applicability_domain'] == True]

    if df_base.empty:
        print("SHAP: 无可解释样本，跳过。")
        return

    smiles_col = 'canonical_smiles' if 'canonical_smiles' in df_base.columns and df_base['canonical_smiles'].notna().any() else 'smiles'
    smiles_list = df_base[smiles_col].astype(str).tolist()
    orig_indices = df_base.index.to_list()

    # 降采样（可复现）
    rng = random.Random(args.shap_seed)
    max_n = args.shap_downsample if args.shap_downsample and args.shap_downsample > 0 else len(smiles_list)
    if len(smiles_list) > max_n:
        idx_selected = sorted(rng.sample(range(len(smiles_list)), max_n))
        smiles_list = [smiles_list[i] for i in idx_selected]
        orig_indices = [orig_indices[i] for i in idx_selected]

    # 构建特征矩阵
    X, _ = build_feature_matrix_from_smiles(predictor, smiles_list)
    if X.size == 0:
        print("SHAP: 未能构建任何特征，跳过。")
        return
    n_features = X.shape[1]
    feature_names = get_feature_names_from_model(n_features)

    # 输出目录
    root = Path(args.shap_output or "shap_analysis")
    ts = datetime.now().strftime("%Y%m%d-%H%M%S")
    safe_ds = "".join([c if c.isalnum() or c in ("-", "_") else "_" for c in (dataset_id or "dataset")])
    out_dir = root / f"{safe_ds}_{ts}"
    out_dir.mkdir(parents=True, exist_ok=True)

    # 选择解释器
    explainer = None
    shap_values = None
    try_tree = args.shap_explainer in ("auto", "tree")
    if try_tree:
        try:
            explainer = shap.TreeExplainer(predictor.model)
            shap_values = explainer.shap_values(X)
        except Exception as e:
            print(f"SHAP: TreeExplainer 失败，回退 KernelExplainer。原因: {e}")

    if shap_values is None:
        # 背景集
        bg_n = min(int(args.shap_background or 100), X.shape[0])
        background = X[:bg_n, :]
        try:
            explainer = shap.KernelExplainer(predictor.model.predict, background)
            shap_values = explainer.shap_values(X, nsamples="auto")
        except Exception as e:
            print(f"SHAP: KernelExplainer 失败: {e}")
            return

    # 绘图：bar图显示正负SHAP值
    try:
        # 生成显示正负SHAP值的条形图
        mean_shap = np.mean(shap_values, axis=0)  # 保留正负号

        # 按绝对值排序，但保留正负号
        sorted_indices = np.argsort(np.abs(mean_shap))[::-1]
        top_n = min(20, len(sorted_indices))  # 显示Top 20特征
        top_indices = sorted_indices[:top_n]

        top_mean_shap = mean_shap[top_indices]
        top_feature_names = [feature_names[i] for i in top_indices]

        # 创建正负条形图
        plt.figure(figsize=(10, 8))
        colors = ['red' if x > 0 else 'blue' for x in top_mean_shap]
        plt.barh(range(len(top_mean_shap)), top_mean_shap, color=colors, alpha=0.7)

        plt.yticks(range(len(top_feature_names)), top_feature_names)
        plt.xlabel('Mean SHAP Value')
        plt.title('Feature Importance with Toxic Effects')
        plt.axvline(x=0, color='black', linestyle='-', alpha=0.3)

        # 添加图例
        from matplotlib.patches import Patch
        legend_elements = [Patch(facecolor='red', alpha=0.7, label='Positive Effect'),
                          Patch(facecolor='blue', alpha=0.7, label='Negative Effect')]
        plt.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(0.98, 0.98))

        plt.gca().invert_yaxis()
        plt.tight_layout()
        plt.savefig(str(out_dir / "bar_plot.png"), dpi=200)
        plt.close()
    except Exception as e:
        print(f"SHAP: 绘制 bar 图失败: {e}")

    # 生成SHAP特征排序摘要
    try:
        # 计算平均绝对SHAP值和平均SHAP值
        mean_abs_shap = np.mean(np.abs(shap_values), axis=0)
        mean_shap = np.mean(shap_values, axis=0)

        # 按绝对值排序
        sorted_indices = np.argsort(-mean_abs_shap)

        # 生成SHAP值排序摘要
        summary_file = out_dir / "shap_feature_ranking.txt"
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("SHAP特征重要性排序摘要\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"分析时间: {ts}\n")
            f.write(f"分析样本数: {shap_values.shape[0]}\n")
            f.write(f"特征总数: {shap_values.shape[1]}\n")
            f.write(f"基线预测值: {explainer.expected_value:.4f}\n\n")

            f.write("特征重要性排序 (按平均绝对SHAP值排序):\n")
            f.write("-" * 80 + "\n")
            f.write(f"{'排名':<4} {'特征名称':<30} {'平均SHAP值':<12} {'平均|SHAP值|':<12} {'效应方向':<10}\n")
            f.write("-" * 80 + "\n")

            for rank, idx in enumerate(sorted_indices, 1):
                feature_name = feature_names[idx]
                mean_shap_val = mean_shap[idx]
                mean_abs_val = mean_abs_shap[idx]
                effect_direction = "正向" if mean_shap_val > 0 else "负向" if mean_shap_val < 0 else "中性"

                f.write(f"{rank:<4} {feature_name:<30} {mean_shap_val:<+12.6f} {mean_abs_val:<12.6f} {effect_direction:<10}\n")

                # 只显示前50个最重要的特征，避免文件过长
                if rank >= 50:
                    f.write(f"... (仅显示前50个最重要特征)\n")
                    break

            f.write("\n" + "=" * 50 + "\n")
            f.write("说明:\n")
            f.write("- 平均SHAP值: 该特征对预测的平均贡献 (正值=增加毒性, 负值=降低毒性)\n")
            f.write("- 平均|SHAP值|: 该特征的平均重要性程度 (绝对值越大越重要)\n")
            f.write("- 效应方向: 该特征的主要作用方向\n")

        print(f"SHAP特征排序摘要已保存到: {summary_file}")

    except Exception as e:
        print(f"SHAP: 生成特征排序摘要失败: {e}")

    print(f"SHAP: 解释完成，输出目录: {out_dir}")

# =============================================================================
# 类似物筛查功能
# =============================================================================

def calculate_molecular_similarity(query_smiles, reference_smiles):
    """计算两个分子的Tanimoto相似性"""
    try:
        query_mol = Chem.MolFromSmiles(query_smiles)
        ref_mol = Chem.MolFromSmiles(reference_smiles)

        if query_mol is None or ref_mol is None:
            return 0.0

        # 生成Morgan指纹
        query_fp = AllChem.GetMorganFingerprintAsBitVect(query_mol, 2, nBits=2048)
        ref_fp = AllChem.GetMorganFingerprintAsBitVect(ref_mol, 2, nBits=2048)

        # 计算Tanimoto相似性
        similarity = DataStructs.TanimotoSimilarity(query_fp, ref_fp)
        return similarity
    except Exception as e:
        print(f"计算相似性失败: {e}")
        return 0.0

def visualize_molecule(smiles, output_dir, filename_prefix="molecule"):
    """
    可视化分子结构并保存为图片

    Args:
        smiles: 分子的SMILES字符串
        output_dir: 输出目录
        filename_prefix: 文件名前缀

    Returns:
        保存的图片文件路径，如果失败则返回None
    """
    try:
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            print(f"无法解析SMILES: {smiles}")
            return None

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 清理SMILES字符串作为文件名（移除特殊字符）
        safe_smiles = "".join(c if c.isalnum() or c in ('-', '_') else '_' for c in smiles)
        # 限制文件名长度
        if len(safe_smiles) > 50:
            safe_smiles = safe_smiles[:50]

        filename = f"{filename_prefix}_{safe_smiles}.png"
        filepath = os.path.join(output_dir, filename)

        # 生成分子图像
        img = Draw.MolToImage(mol, size=(400, 400))
        img.save(filepath)

        print(f"  分子结构已保存: {filepath}")
        return filepath

    except Exception as e:
        print(f"分子可视化失败 {smiles}: {e}")
        return None

def find_similar_compounds(query_data, training_data_path, similarity_threshold=0.3):
    """
    在训练集中寻找类似物

    Args:
        query_data: 查询数据，可以是SMILES列表或包含SMILES和source的DataFrame
        training_data_path: 训练数据路径
        similarity_threshold: 相似性阈值 (默认0.3即30%)

    Returns:
        dict: 每个查询分子的类似物信息
    """
    print(f"\n步骤4: 类似物筛查分析...")
    print(f"相似性阈值: {similarity_threshold:.1%}")

    # 加载训练数据
    try:
        df_train = pd.read_excel(training_data_path)
        if 'smiles' not in df_train.columns:
            print("训练数据中缺少smiles列")
            return {}

        train_smiles = df_train['smiles'].dropna().tolist()
        print(f"训练集化合物数量: {len(train_smiles)}")

    except Exception as e:
        print(f"加载训练数据失败: {e}")
        return {}

    # 处理输入数据格式
    if isinstance(query_data, list):
        # 如果是SMILES列表，转换为DataFrame
        query_df = pd.DataFrame({'smiles': query_data})
    elif isinstance(query_data, pd.DataFrame):
        # 如果是DataFrame，直接使用
        query_df = query_data.copy()
    else:
        print("错误：query_data必须是SMILES列表或DataFrame")
        return {}

    results = {}

    for i, row in query_df.iterrows():
        query_smiles = row['smiles']
        query_source = row.get('source', 'Unknown')  # 获取source信息，如果没有则为Unknown

        print(f"分析化合物 {i+1}/{len(query_df)}: {query_smiles[:50]}...")

        similarities = []

        # 计算与训练集中每个化合物的相似性
        for j, train_smiles in enumerate(train_smiles):
            similarity = calculate_molecular_similarity(query_smiles, train_smiles)
            if similarity >= similarity_threshold:
                # 获取对应的y值和source
                y_value = df_train.iloc[j]['y'] if 'y' in df_train.columns else None
                train_source = df_train.iloc[j]['source'] if 'source' in df_train.columns else 'Unknown'
                similarities.append({
                    'train_smiles': train_smiles,
                    'similarity': similarity,
                    'y_value': y_value,
                    'train_source': train_source,
                    'train_index': j
                })

        # 按相似性排序
        similarities.sort(key=lambda x: x['similarity'], reverse=True)

        # 统计结果
        total_similar = len(similarities)
        top5_similar = similarities[:5]

        results[query_smiles] = {
            'total_count': total_similar,
            'top5_similar': top5_similar,
            'threshold': similarity_threshold,
            'source': query_source  # 添加source信息
        }

        # 输出结果
        print(f"  找到 {total_similar} 个相似化合物 (相似性 ≥ {similarity_threshold:.1%})")
        if top5_similar:
            print(f"  相似性最高的5个化合物:")
            for k, sim_info in enumerate(top5_similar, 1):
                y_str = f", y={sim_info['y_value']:.4f}" if sim_info['y_value'] is not None else ""
                source_str = f", source={sim_info.get('train_source', 'Unknown')}"
                print(f"    {k}. 相似性: {sim_info['similarity']:.3f} ({sim_info['similarity']:.1%}){y_str}{source_str}")
                print(f"       SMILES: {sim_info['train_smiles']}")

                # 可视化类似物分子结构
                visualize_molecule(
                    sim_info['train_smiles'],
                    "result/similar_molecules",
                    f"similar_{i}_{k}"
                )
        else:
            print(f"  未找到相似性 ≥ {similarity_threshold:.1%} 的化合物")
        print()

    return results

def save_similarity_analysis(results, output_dir):
    """保存类似物分析结果到文件"""
    try:
        output_dir = Path(output_dir)
        output_dir.mkdir(exist_ok=True)

        similarity_file = output_dir / "similarity_analysis.txt"

        with open(similarity_file, 'w', encoding='utf-8') as f:
            f.write("类似物筛查分析结果\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"查询化合物数量: {len(results)}\n")
            f.write(f"查询化合物结构图保存位置: result/query_molecules/\n")
            f.write(f"类似物结构图保存位置: result/similar_molecules/\n\n")

            for i, (query_smiles, result) in enumerate(results.items(), 1):
                f.write(f"查询化合物 {i}:\n")
                f.write(f"SMILES: {query_smiles}\n")
                f.write(f"Source: {result.get('source', 'Unknown')}\n")
                f.write(f"相似性阈值: {result['threshold']:.1%}\n")
                f.write(f"找到类似物数量: {result['total_count']}\n\n")

                if result['top5_similar']:
                    f.write("相似性最高的5个化合物:\n")
                    f.write("-" * 120 + "\n")
                    f.write(f"{'排名':<4} {'相似性':<10} {'y值':<12} {'训练集SMILES':<50} {'Source':<40}\n")
                    f.write("-" * 120 + "\n")

                    for j, sim_info in enumerate(result['top5_similar'], 1):
                        y_str = f"{sim_info['y_value']:.4f}" if sim_info['y_value'] is not None else "N/A"
                        source_str = sim_info.get('train_source', 'Unknown')
                        f.write(f"{j:<4} {sim_info['similarity']:.3f}      {y_str:<12} {sim_info['train_smiles']:<50} {source_str:<40}\n")
                else:
                    f.write("未找到符合阈值的类似物\n")

                f.write("\n" + "="*50 + "\n\n")

        print(f"类似物分析结果已保存到: {similarity_file}")

    except Exception as e:
        print(f"保存类似物分析结果失败: {e}")



def main():
    """主函数"""
    # 参数
    parser = argparse.ArgumentParser(description="LightGBM预测与应用域分析（内置 SHAP 和类似物筛查）")
    parser.add_argument("--input-file", type=str, default=None, help="包含 smiles 列的 Excel 文件路径")
    parser.add_argument("--shap", action="store_true", default=True, help="启用 SHAP 解释与可视化（默认启用）")
    parser.add_argument("--shap-scope", choices=["all", "ad"], default="ad", help="解释样本范围")
    parser.add_argument("--shap-downsample", type=int, default=200, help="解释样本最大数量（降采样）")
    parser.add_argument("--shap-explainer", choices=["auto", "tree", "kernel"], default="auto", help="解释器类型")
    parser.add_argument("--shap-topk", type=int, default=10, help="dependence 图的特征数")
    parser.add_argument("--dataset-id", type=str, default=None, help="数据集标识（默认取输入文件名）")
    parser.add_argument("--shap-output", type=str, default="shap_analysis", help="输出根目录")
    parser.add_argument("--shap-seed", type=int, default=42, help="随机种子")
    parser.add_argument("--shap-background", type=int, default=100, help="KernelExplainer 背景样本数")
    args = parser.parse_args()

    # 要预测的SMILES列表
    test_smiles = [
        "CCO",
    ]
    if args.input_file:
        try:
            df_in = pd.read_excel(args.input_file)
            if 'smiles' in df_in.columns:
                test_smiles = df_in['smiles'].dropna().astype(str).tolist()
            else:
                print(f"输入文件缺少 smiles 列，使用默认示例。")
        except Exception as e:
            print(f"读取输入文件失败：{e}，使用默认示例。")

    print("========== LightGBM模型预测与应用域分析 ==========")
    print(f"总共预测 {len(test_smiles)} 个化合物")
    print()

    # 创建预测器实例
    predictor = LightGBMPredictor(MODEL_PATH)

    # 批量预测
    try:
        print("步骤1: 进行LightGBM预测...")
        results = predictor.predict_batch(smiles_list=test_smiles)

        # 将结果转换为DataFrame
        results_df = pd.DataFrame(results)

        print("步骤2: 进行应用域分析...")
        # 加载训练数据
        df_train = load_training_data(TRAINING_DATA_PATH)

        # 计算应用域指标
        df_with_metrics = calculate_ad_metrics(df_train, results_df)

        # 应用应用域判断标准
        df_final = apply_ad_criteria(df_with_metrics, OPTIMAL_DENSLB, OPTIMAL_LDUB)

        # 确保输出目录存在
        os.makedirs("result", exist_ok=True)

        # 保存到Excel文件
        output_file = "result/batch_prediction_with_AD_results.xlsx"
        df_final.to_excel(output_file, index=False, engine='openpyxl')

        print("\n======== 预测和应用域分析完成 ========")
        print(f"结果已保存到: {output_file}")

        # SHAP 解释（默认启用）
        if args.shap:
            print("\n步骤3: 进行SHAP可解释性分析...")
            try:
                dsid = args.dataset_id or (Path(args.input_file).stem if args.input_file else "batch")
                run_shap_explanation(df_final, predictor, args, dsid, args.input_file)
            except Exception as e:
                print(f"SHAP分析失败: {e}")
                print("继续显示预测结果...")
                import traceback
                traceback.print_exc()

        # 类似物筛查分析
        try:
            # 获取有效的数据（包含source信息）
            if 'error' in df_final.columns:
                valid_mask = (df_final['error'].isna()) | (df_final['error'] == '') | (df_final['error'].isnull())
                valid_data = df_final[valid_mask][['smiles', 'source']].copy() if 'source' in df_final.columns else df_final[valid_mask][['smiles']].copy()
            else:
                valid_data = df_final[['smiles', 'source']].copy() if 'source' in df_final.columns else df_final[['smiles']].copy()

            if len(valid_data) > 0:
                # 首先为查询化合物生成可视化
                print("生成查询化合物的分子结构图...")
                for idx, row in valid_data.iterrows():
                    visualize_molecule(
                        row['smiles'],
                        "result/query_molecules",
                        f"query_{idx+1}"
                    )

                # 进行类似物筛查
                similarity_results = find_similar_compounds(
                    valid_data,
                    TRAINING_DATA_PATH,
                    similarity_threshold=0.3
                )

                # 保存类似物分析结果
                save_similarity_analysis(similarity_results, "result")
            else:
                print("没有有效的SMILES进行类似物分析")
        except Exception as e:
            print(f"类似物筛查失败: {e}")
            import traceback
            traceback.print_exc()

        print("\n预测结果摘要:")

        # 应用域统计
        ad_inside = len(df_final[df_final['in_applicability_domain'] == True])
        ad_outside = len(df_final[df_final['in_applicability_domain'] == False])

        print(f"总预测化合物数: {len(df_final)}")
        print(f"应用域内化合物: {ad_inside}")
        print(f"应用域外化合物: {ad_outside}")
        print()

        # 显示前5个结果的详细信息
        for i, (_, row) in enumerate(df_final.head(5).iterrows()):
            print(f"化合物 {i+1}: {row['smiles'][:30]}...")
            if 'prediction_log' in row and pd.notna(row['prediction_log']):
                print(f"  负对数预测值: {row['prediction_log']:.4f}")
                print(f"  原数值预测值: {row['prediction']:.6f} mol/L")
            else:
                print(f"  预测值: {row['prediction']:.4f}")
            ad_status = "应用域内" if row['in_applicability_domain'] else "应用域外"
            print(f"  应用域状态: {ad_status}")
            print(f"  相似性密度: {row['ad_density_value']:.4f} (阈值: {OPTIMAL_DENSLB})")
            print(f"  局域不连续性: {row['ad_ld_value']:.4f} (阈值: {OPTIMAL_LDUB})")
            print(f"  判断原因: {row['ad_reason']}")
            print()

    except Exception as e:
        print(f"预测失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
