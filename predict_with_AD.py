#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
机器学习回归模型预测与应用域判断整合脚本

功能:
1. 使用训练好的机器学习模型进行回归预测（支持PubChem指纹）
2. 使用预设的最佳参数判断是否在应用域内
3. SHAP可解释性分析
4. 类似物筛查功能
5. 输出整合结果

使用方法:
1. 在下面的配置区域设置你的参数
2. 运行: python predict_with_AD.py
"""

import pandas as pd
import numpy as np
import os
import pickle
from rdkit import Chem
from rdkit.Chem import DataStructs, AllChem, Draw
import warnings
warnings.filterwarnings('ignore')

# 追加：命令行与工具
import argparse
from pathlib import Path
from datetime import datetime
import random

# 绘图：使用非交互后端，避免服务器/命令行报错
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt

# 导入应用域模块
from adsal import NSG

# =============================================================================
# 配置参数 - 请在这里修改你的设置
# =============================================================================

# 输出文件配置
OUTPUT_FILE = 'prediction_with_AD_results.xlsx'  # 输出文件名

# 最佳应用域参数
OPTIMAL_DENSLB = 1e-10    # 最佳相似性密度阈值
OPTIMAL_LDUB = 0.9       # 最佳局域不连续性阈值

# 模型和数据路径
MODEL_PATH = 'saved_model/lightgbm_model_PubChem.pkl'  # 模型路径（支持多种模型类型和指纹）
TRAINING_DATA_PATH = 'TrainingSet_Regression.xlsx'  # 训练集数据路径

# =============================================================================
# 注意：
# 1. 请确保你已经训练并保存了模型（支持PubChem、Mordred、MACCS等指纹）
# 2. 将获得的最佳densLB和LdUB值填入上面的OPTIMAL_DENSLB和OPTIMAL_LDUB
# 3. 修改INPUT_FILE为你要分析的化合物文件路径
# 4. 本脚本支持PubChem指纹、SHAP分析和类似物筛查功能
# 5. 运行: python predict_with_AD.py [--input-file your_file.xlsx] [--shap]
# =============================================================================

# =============================================================================
# 预测功能类和函数
# =============================================================================

class LightGBMPredictor:
    """LightGBM回归预测器（支持PubChem指纹）"""

    def __init__(self, model_path):
        """初始化预测器"""
        self.model_path = model_path
        self._load_model()

    def _load_model(self):
        """加载模型和相关组件"""
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型文件不存在: {self.model_path}")

        print(f"加载模型: {self.model_path}")

        with open(self.model_path, 'rb') as f:
            model_components = pickle.load(f)

        self.model = model_components['model']
        self.scaler = model_components['scaler']
        self.max_length = model_components['max_length']
        self.fingerprint_column = model_components.get('fingerprint_column', 'PubChem')
        print(f"模型加载成功")
        print(f"使用PubChem指纹进行预测")

    def _validate_smiles(self, smiles):
        """验证SMILES字符串"""
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                return False, "无效的SMILES字符串"

            canonical_smiles = Chem.MolToSmiles(mol, isomericSmiles=True)
            return True, canonical_smiles
        except Exception as e:
            return False, f"SMILES处理错误: {str(e)}"

    def _generate_fingerprint(self, smiles):
        """生成PubChem指纹（用于预测）"""
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                return []

            # 使用PubChem指纹
            from skfp.fingerprints import PubChemFingerprint
            pubchem_fp = PubChemFingerprint()
            fp_array = pubchem_fp.transform([smiles])[0]
            return fp_array.tolist()

        except Exception as e:
            print(f"PubChem指纹生成错误: {e}")
            return []

    def _preprocess_single_sample(self, smiles):
        """预处理单个样本（仅使用指纹特征）"""
        # 生成指纹
        fingerprint = self._generate_fingerprint(smiles)

        if not fingerprint:
            raise ValueError("指纹生成失败")

        # 将指纹转换为numpy数组
        X = np.array(fingerprint).reshape(1, -1)

        # 确保指纹长度与训练时一致
        if hasattr(self, 'max_length') and X.shape[1] != self.max_length:
            # 如果长度不匹配，进行填充或截断
            if X.shape[1] < self.max_length:
                # 填充0
                padding = np.zeros((1, self.max_length - X.shape[1]))
                X = np.hstack([X, padding])
            else:
                # 截断
                X = X[:, :self.max_length]

        # 特征缩放
        X_scaled = self.scaler.transform(X)

        return X_scaled

    def predict_single(self, smiles):
        """预测单个SMILES"""
        is_valid, result = self._validate_smiles(smiles)
        if not is_valid:
            return {'smiles': smiles, 'error': result}

        canonical_smiles = result

        try:
            # 预处理
            X_scaled = self._preprocess_single_sample(canonical_smiles)

            # 预测 (负对数形式)
            log_prediction = self.model.predict(X_scaled)[0]

            # 转换为原数值 (10^(-log_prediction))
            original_value = 10**(-log_prediction)

            return {
                'smiles': smiles,
                'canonical_smiles': canonical_smiles,
                'prediction_log': float(log_prediction),  # 负对数预测值
                'prediction': float(original_value),      # 原数值预测值
                'error': None
            }

        except Exception as e:
            return {'smiles': smiles, 'error': f"预测错误: {str(e)}"}

    def predict_batch(self, smiles_list):
        """
        批量预测SMILES列表

        Args:
            smiles_list: SMILES字符串列表

        Returns:
            预测结果列表，每个元素包含：
            - smiles: 原始SMILES
            - canonical_smiles: 规范化SMILES
            - prediction: 预测值
            - error: 错误信息 (如果没有错误则为None)
        """
        results = []

        print(f"批量预测 {len(smiles_list)} 个化合物...")

        for i, smiles in enumerate(smiles_list):
            if i % 10 == 0 and i > 0:
                print(f"已处理 {i}/{len(smiles_list)} 个化合物...")

            result = self.predict_single(smiles)
            results.append(result)

        print(f"批量预测完成！")
        return results

def predict_on_input_file(input_file_path, model_path, output_file_path=None):
    """
    对输入文件进行预测

    Args:
        input_file_path: 输入文件路径（Excel格式）
        model_path: 模型文件路径
        output_file_path: 输出文件路径，如果为None则在原文件名后加_predicted
    """
    print("开始对输入文件进行LightGBM回归预测...")

    # 1. 检查输入文件
    if not os.path.exists(input_file_path):
        print(f"输入文件不存在: {input_file_path}")
        return

    # 2. 确定输出文件路径
    if output_file_path is None:
        base_name = os.path.splitext(input_file_path)[0]
        output_file_path = f"{base_name}_lightgbm_predicted.xlsx"

    print(f"输入文件: {input_file_path}")
    print(f"模型文件: {model_path}")
    print(f"输出文件: {output_file_path}")

    # 3. 加载预测器
    try:
        predictor = LightGBMPredictor(model_path)
    except Exception as e:
        print(f"模型加载失败: {str(e)}")
        return

    # 4. 加载输入数据
    try:
        df = pd.read_excel(input_file_path)
        print(f"样本数量: {len(df)}")

        # 检查必要列
        if 'smiles' not in df.columns:
            print("输入数据缺少必要列: smiles")
            return

    except Exception as e:
        print(f"数据加载失败: {str(e)}")
        return

    # 5. 准备结果DataFrame
    result_df = df.copy()
    result_df['canonical_smiles'] = ''
    result_df['prediction_log'] = np.nan  # 负对数预测值
    result_df['prediction'] = np.nan      # 原数值预测值
    result_df['error'] = ''

    # 6. 进行预测
    print("开始预测...")
    for idx, row in df.iterrows():
        smiles = row['smiles']
        result = predictor.predict_single(smiles)

        if result['error']:
            result_df.loc[idx, 'error'] = result['error']
        else:
            result_df.loc[idx, 'canonical_smiles'] = result['canonical_smiles']
            result_df.loc[idx, 'prediction_log'] = result['prediction_log']  # 负对数值
            result_df.loc[idx, 'prediction'] = result['prediction']          # 原数值

    # 7. 保存结果
    try:
        result_df.to_excel(output_file_path, index=False)
        print(f"结果已保存到: {output_file_path}")

        # 显示统计结果
        valid_predictions = result_df[result_df['error'] == '']
        if len(valid_predictions) > 0:
            print(f"\n预测统计:")
            print(f"有效预测: {len(valid_predictions)}/{len(result_df)}")
            print(f"负对数预测值 - 平均: {valid_predictions['prediction_log'].mean():.4f}, 范围: {valid_predictions['prediction_log'].min():.4f} - {valid_predictions['prediction_log'].max():.4f}")
            print(f"原数值预测值 - 平均: {valid_predictions['prediction'].mean():.6f} mol/L, 范围: {valid_predictions['prediction'].min():.6f} - {valid_predictions['prediction'].max():.6f} mol/L")

        errors = result_df[result_df['error'] != '']
        if len(errors) > 0:
            print(f"无法预测样本: {len(errors)}")

    except Exception as e:
        print(f"保存结果失败: {str(e)}")

# exp权重函数
def expWt(x, a=15, eps=1e-6):
    """指数权重函数"""
    return np.exp(-a*(1-x)/(x + eps))

EXP_WEIGHT_PARAMS = {'a': 10}

# =============================================================================
# 核心功能函数
# =============================================================================

def load_training_data(file_path):
    """加载训练集数据"""
    print(f"加载训练集数据: {file_path}")
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"训练数据文件不存在: {file_path}")
    df = pd.read_excel(file_path)
    required_cols = ['smiles', 'y']
    target_col = 'y'
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"训练集缺少必要列: {missing_cols}")

    # 准备数据
    df_clean = df[required_cols].copy()
    if target_col != 'y':
        df_clean.rename(columns={target_col: 'y'}, inplace=True)  # 重命名以保持一致性
    df_clean.reset_index(drop=True, inplace=True)

    print(f"训练集样本数: {len(df_clean)}")
    print(f"使用目标变量列: {target_col}")
    return df_clean

def run_prediction(input_file, model_path, temp_output):
    """运行回归预测"""
    print("步骤1: 运行LightGBM回归预测")

    if not os.path.exists(model_path):
        raise FileNotFoundError(f"模型文件不存在: {model_path}")

    # 运行预测
    predict_on_input_file(input_file, model_path, temp_output)

    if not os.path.exists(temp_output):
        raise RuntimeError("预测失败，未生成预测文件")

    print("预测完成")
    return temp_output

def calculate_ad_metrics(df_train, df_query):
    """计算应用域指标"""
    print("步骤2: 计算应用域指标")

    # 创建NSG对象
    nsg = NSG(df_train, yCol='y', smiCol='smiles')

    # 计算分子指纹相似性
    nsg.calcPairwiseSimilarityWithFp('MACCS_keys')

    # 生成查询-训练相似性矩阵
    dfQTSM = nsg.genQTSM(df_query, 'smiles')

    # 计算应用域指标（使用exp权重函数）
    ad_metrics = nsg.queryADMetrics(
        dfQTSM,
        wtFunc1=expWt,
        kw1=EXP_WEIGHT_PARAMS,
        wtFunc2=expWt,
        kw2=EXP_WEIGHT_PARAMS,
        code='|exp'
    )

    # 合并结果
    df_result = df_query.join(ad_metrics)

    print("应用域指标计算完成")
    return df_result

def apply_ad_criteria(df_with_metrics, optimal_densLB, optimal_LdUB):
    """应用应用域判断标准"""
    print("步骤3: 应用域判断")

    print(f"相似性密度阈值 (densLB): {optimal_densLB}")
    print(f"局域不连续性阈值 (LdUB): {optimal_LdUB}")

    # 应用域判断条件
    ad_condition = (
        (df_with_metrics['simiDensity|exp'] >= optimal_densLB) &
        (df_with_metrics['simiWtLD_w|exp'] <= optimal_LdUB)
    )

    # 添加应用域判断结果
    df_result = df_with_metrics.copy()
    df_result['in_applicability_domain'] = ad_condition
    df_result['ad_densLB_threshold'] = optimal_densLB
    df_result['ad_LdUB_threshold'] = optimal_LdUB
    df_result['ad_density_value'] = df_with_metrics['simiDensity|exp']
    df_result['ad_ld_value'] = df_with_metrics['simiWtLD_w|exp']

    # 添加应用域判断原因
    def get_ad_reason(row):
        if row['in_applicability_domain']:
            return "在应用域内"
        else:
            reasons = []
            if row['ad_density_value'] < optimal_densLB:
                reasons.append(f"相似性密度({row['ad_density_value']:.3f}) < 阈值({optimal_densLB})")
            if row['ad_ld_value'] > optimal_LdUB:
                reasons.append(f"局域不连续性({row['ad_ld_value']:.3f}) > 阈值({optimal_LdUB})")
            return "; ".join(reasons)

    df_result['ad_reason'] = df_result.apply(get_ad_reason, axis=1)

    # 统计结果
    total_compounds = len(df_result)
    in_domain_count = df_result['in_applicability_domain'].sum()

    print(f"应用域判断结果:")
    print(f"总化合物数: {total_compounds}")
    print(f"应用域内: {in_domain_count} ({in_domain_count/total_compounds*100:.1f}%)")
    print(f"应用域外: {total_compounds - in_domain_count} ({(total_compounds - in_domain_count)/total_compounds*100:.1f}%)")

    return df_result

def generate_summary(df_result):
    """生成结果摘要"""
    print("\n结果摘要:")
    print("=" * 50)

    for idx, row in df_result.iterrows():
        compound_name = row.get('compound_name', f'化合物_{idx+1}')
        status = "应用域内" if row['in_applicability_domain'] else "应用域外"

        print(f"\n{compound_name}:")
        print(f"  SMILES: {row['smiles']}")

        # 显示两种预测值
        if 'prediction_log' in row and pd.notna(row['prediction_log']):
            print(f"  负对数预测值: {row['prediction_log']:.4f}")
            print(f"  原数值预测值: {row['prediction']:.6f} mol/L")
        else:
            print(f"  预测值: {row.get('prediction', 'N/A'):.4f}")

        print(f"  应用域状态: {status}")
        print(f"  相似性密度: {row['ad_density_value']:.4f}")
        print(f"  局域不连续性: {row['ad_ld_value']:.4f}")
        print(f"  判断原因: {row['ad_reason']}")

        # 给出建议
        if row['in_applicability_domain']:
            suggestion = "预测结果可信，建议采用"
        else:
            suggestion = "预测结果可信度较低，建议实验验证"
        print(f"  建议: {suggestion}")

    print("=" * 50)

# =============================================================================
# SHAP 集成函数
# =============================================================================

def build_feature_matrix_from_smiles(predictor, smiles_list):
    """
    基于 predictor 的单样本预处理，构建批量特征矩阵（缩放后）
    返回:
      X: np.ndarray [n_samples, n_features]
      ok_indices: 在输入 smiles_list 中成功构建特征的局部索引列表
    """
    print(f"SHAP: 为 {len(smiles_list)} 个化合物构建特征矩阵...")
    X_list = []
    ok_indices = []
    for i, s in enumerate(smiles_list):
        try:
            X_scaled = predictor._preprocess_single_sample(s)
            if X_scaled is not None and X_scaled.size > 0:
                X_list.append(X_scaled[0])
                ok_indices.append(i)
        except Exception:
            # 忽略个别失败样本
            continue
    if len(X_list) == 0:
        return np.empty((0, 0)), []
    X = np.vstack(X_list)
    print(f"SHAP: 成功构建了 {X.shape[0]} 个样本的特征矩阵，特征维度: {X.shape[1]}")
    return X, ok_indices

def get_generic_feature_names(n_features):
    """生成通用特征名 f0..f{n-1}"""
    return [f"f{i}" for i in range(n_features)]



def get_pubchem_feature_names():
    """获取PubChem指纹的真实特征名称"""
    try:
        from skfp.fingerprints import PubChemFingerprint
        pubchem_fp = PubChemFingerprint()
        # 获取PubChem指纹的特征名称
        feature_names = pubchem_fp.get_feature_names()
        return feature_names
    except ImportError:
        print("警告: skfp包未安装，无法获取PubChem特征名称")
        return None
    except AttributeError:
        print("警告: 当前skfp版本不支持get_feature_names方法")
        # 如果没有get_feature_names方法，使用已知的PubChem特征名称
        return get_known_pubchem_features()
    except Exception as e:
        print(f"警告: 获取PubChem特征名称失败: {e}")
        return None 

def get_known_pubchem_features():
    """返回已知的PubChem指纹特征名称（完整881位）
    基于PubChem官方文档：https://github.com/cdk/orchem/blob/master/doc/pubchem_fingerprints.txt
    """
    # Section 1: Hierarchic Element Counts (Bits 0-114)
    pubchem_features = [
        ">=4 H", ">=8 H", ">=16 H", ">=32 H",  # 0-3
        ">=1 Li", ">=2 Li",  # 4-5
        ">=1 B", ">=2 B", ">=4 B",  # 6-8
        ">=2 C", ">=4 C", ">=8 C", ">=16 C", ">=32 C",  # 9-13
        ">=1 N", ">=2 N", ">=4 N", ">=8 N",  # 14-17
        ">=1 O", ">=2 O", ">=4 O", ">=8 O", ">=16 O",  # 18-22
        ">=1 F", ">=2 F", ">=4 F",  # 23-25
        ">=1 Na", ">=2 Na",  # 26-27
        ">=1 Si", ">=2 Si",  # 28-29
        ">=1 P", ">=2 P", ">=4 P",  # 30-32
        ">=1 S", ">=2 S", ">=4 S", ">=8 S",  # 33-36
        ">=1 Cl", ">=2 Cl", ">=4 Cl", ">=8 Cl",  # 37-40
        ">=1 K", ">=2 K",  # 41-42
        ">=1 Br", ">=2 Br", ">=4 Br",  # 43-45
        ">=1 I", ">=2 I", ">=4 I",  # 46-48
        ">=1 Be", ">=1 Mg", ">=1 Al", ">=1 Ca", ">=1 Sc", ">=1 Ti", ">=1 V", ">=1 Cr", ">=1 Mn", ">=1 Fe",  # 49-58
        ">=1 Co", ">=1 Ni", ">=1 Cu", ">=1 Zn", ">=1 Ga", ">=1 Ge", ">=1 As", ">=1 Se", ">=1 Kr", ">=1 Rb",  # 59-68
        ">=1 Sr", ">=1 Y", ">=1 Zr", ">=1 Nb", ">=1 Mo", ">=1 Ru", ">=1 Rh", ">=1 Pd", ">=1 Ag", ">=1 Cd",  # 69-78
        ">=1 In", ">=1 Sn", ">=1 Sb", ">=1 Te", ">=1 Xe", ">=1 Cs", ">=1 Ba", ">=1 Lu", ">=1 Hf", ">=1 Ta",  # 79-88
        ">=1 W", ">=1 Re", ">=1 Os", ">=1 Ir", ">=1 Pt", ">=1 Au", ">=1 Hg", ">=1 Tl", ">=1 Pb", ">=1 Bi",  # 89-98
        ">=1 La", ">=1 Ce", ">=1 Pr", ">=1 Nd", ">=1 Pm", ">=1 Sm", ">=1 Eu", ">=1 Gd", ">=1 Tb", ">=1 Dy",  # 99-108
        ">=1 Ho", ">=1 Er", ">=1 Tm", ">=1 Yb", ">=1 Tc", ">=1 U"  # 109-114
    ]

    # Section 2: Rings in ESSSR ring set (Bits 115-262)
    ring_features = [
        # Ring size 3 (115-128)
        ">=1 any ring size 3", ">=1 saturated/aromatic carbon-only ring size 3",
        ">=1 saturated/aromatic nitrogen-containing ring size 3", ">=1 saturated/aromatic heteroatom-containing ring size 3",
        ">=1 unsaturated non-aromatic carbon-only ring size 3", ">=1 unsaturated non-aromatic nitrogen-containing ring size 3",
        ">=1 unsaturated non-aromatic heteroatom-containing ring size 3", ">=2 any ring size 3",
        ">=2 saturated/aromatic carbon-only ring size 3", ">=2 saturated/aromatic nitrogen-containing ring size 3",
        ">=2 saturated/aromatic heteroatom-containing ring size 3", ">=2 unsaturated non-aromatic carbon-only ring size 3",
        ">=2 unsaturated non-aromatic nitrogen-containing ring size 3", ">=2 unsaturated non-aromatic heteroatom-containing ring size 3",

        # Ring size 4 (129-142)
        ">=1 any ring size 4", ">=1 saturated/aromatic carbon-only ring size 4",
        ">=1 saturated/aromatic nitrogen-containing ring size 4", ">=1 saturated/aromatic heteroatom-containing ring size 4",
        ">=1 unsaturated non-aromatic carbon-only ring size 4", ">=1 unsaturated non-aromatic nitrogen-containing ring size 4",
        ">=1 unsaturated non-aromatic heteroatom-containing ring size 4", ">=2 any ring size 4",
        ">=2 saturated/aromatic carbon-only ring size 4", ">=2 saturated/aromatic nitrogen-containing ring size 4",
        ">=2 saturated/aromatic heteroatom-containing ring size 4", ">=2 unsaturated non-aromatic carbon-only ring size 4",
        ">=2 unsaturated non-aromatic nitrogen-containing ring size 4", ">=2 unsaturated non-aromatic heteroatom-containing ring size 4",

        # Ring size 5 (143-177)
        ">=1 any ring size 5", ">=1 saturated/aromatic carbon-only ring size 5",
        ">=1 saturated/aromatic nitrogen-containing ring size 5", ">=1 saturated/aromatic heteroatom-containing ring size 5",
        ">=1 unsaturated non-aromatic carbon-only ring size 5", ">=1 unsaturated non-aromatic nitrogen-containing ring size 5",
        ">=1 unsaturated non-aromatic heteroatom-containing ring size 5", ">=2 any ring size 5",
        ">=2 saturated/aromatic carbon-only ring size 5", ">=2 saturated/aromatic nitrogen-containing ring size 5",
        ">=2 saturated/aromatic heteroatom-containing ring size 5", ">=2 unsaturated non-aromatic carbon-only ring size 5",
        ">=2 unsaturated non-aromatic nitrogen-containing ring size 5", ">=2 unsaturated non-aromatic heteroatom-containing ring size 5",
        ">=3 any ring size 5", ">=3 saturated/aromatic carbon-only ring size 5",
        ">=3 saturated/aromatic nitrogen-containing ring size 5", ">=3 saturated/aromatic heteroatom-containing ring size 5",
        ">=3 unsaturated non-aromatic carbon-only ring size 5", ">=3 unsaturated non-aromatic nitrogen-containing ring size 5",
        ">=3 unsaturated non-aromatic heteroatom-containing ring size 5", ">=4 any ring size 5",
        ">=4 saturated/aromatic carbon-only ring size 5", ">=4 saturated/aromatic nitrogen-containing ring size 5",
        ">=4 saturated/aromatic heteroatom-containing ring size 5", ">=4 unsaturated non-aromatic carbon-only ring size 5",
        ">=4 unsaturated non-aromatic nitrogen-containing ring size 5", ">=4 unsaturated non-aromatic heteroatom-containing ring size 5",
        ">=5 any ring size 5", ">=5 saturated/aromatic carbon-only ring size 5",
        ">=5 saturated/aromatic nitrogen-containing ring size 5", ">=5 saturated/aromatic heteroatom-containing ring size 5",
        ">=5 unsaturated non-aromatic carbon-only ring size 5", ">=5 unsaturated non-aromatic nitrogen-containing ring size 5",
        ">=5 unsaturated non-aromatic heteroatom-containing ring size 5",

        # Ring size 6 (178-212)
        ">=1 any ring size 6", ">=1 saturated/aromatic carbon-only ring size 6",
        ">=1 saturated/aromatic nitrogen-containing ring size 6", ">=1 saturated/aromatic heteroatom-containing ring size 6",
        ">=1 unsaturated non-aromatic carbon-only ring size 6", ">=1 unsaturated non-aromatic nitrogen-containing ring size 6",
        ">=1 unsaturated non-aromatic heteroatom-containing ring size 6", ">=2 any ring size 6",
        ">=2 saturated/aromatic carbon-only ring size 6", ">=2 saturated/aromatic nitrogen-containing ring size 6",
        ">=2 saturated/aromatic heteroatom-containing ring size 6", ">=2 unsaturated non-aromatic carbon-only ring size 6",
        ">=2 unsaturated non-aromatic nitrogen-containing ring size 6", ">=2 unsaturated non-aromatic heteroatom-containing ring size 6",
        ">=3 any ring size 6", ">=3 saturated/aromatic carbon-only ring size 6",
        ">=3 saturated/aromatic nitrogen-containing ring size 6", ">=3 saturated/aromatic heteroatom-containing ring size 6",
        ">=3 unsaturated non-aromatic carbon-only ring size 6", ">=3 unsaturated non-aromatic nitrogen-containing ring size 6",
        ">=3 unsaturated non-aromatic heteroatom-containing ring size 6", ">=4 any ring size 6",
        ">=4 saturated/aromatic carbon-only ring size 6", ">=4 saturated/aromatic nitrogen-containing ring size 6",
        ">=4 saturated/aromatic heteroatom-containing ring size 6", ">=4 unsaturated non-aromatic carbon-only ring size 6",
        ">=4 unsaturated non-aromatic nitrogen-containing ring size 6", ">=4 unsaturated non-aromatic heteroatom-containing ring size 6",
        ">=5 any ring size 6", ">=5 saturated/aromatic carbon-only ring size 6",
        ">=5 saturated/aromatic nitrogen-containing ring size 6", ">=5 saturated/aromatic heteroatom-containing ring size 6",
        ">=5 unsaturated non-aromatic carbon-only ring size 6", ">=5 unsaturated non-aromatic nitrogen-containing ring size 6",
        ">=5 unsaturated non-aromatic heteroatom-containing ring size 6",

        # Ring size 7 (213-226)
        ">=1 any ring size 7", ">=1 saturated/aromatic carbon-only ring size 7",
        ">=1 saturated/aromatic nitrogen-containing ring size 7", ">=1 saturated/aromatic heteroatom-containing ring size 7",
        ">=1 unsaturated non-aromatic carbon-only ring size 7", ">=1 unsaturated non-aromatic nitrogen-containing ring size 7",
        ">=1 unsaturated non-aromatic heteroatom-containing ring size 7", ">=2 any ring size 7",
        ">=2 saturated/aromatic carbon-only ring size 7", ">=2 saturated/aromatic nitrogen-containing ring size 7",
        ">=2 saturated/aromatic heteroatom-containing ring size 7", ">=2 unsaturated non-aromatic carbon-only ring size 7",
        ">=2 unsaturated non-aromatic nitrogen-containing ring size 7", ">=2 unsaturated non-aromatic heteroatom-containing ring size 7",

        # Ring size 8 (227-240)
        ">=1 any ring size 8", ">=1 saturated/aromatic carbon-only ring size 8",
        ">=1 saturated/aromatic nitrogen-containing ring size 8", ">=1 saturated/aromatic heteroatom-containing ring size 8",
        ">=1 unsaturated non-aromatic carbon-only ring size 8", ">=1 unsaturated non-aromatic nitrogen-containing ring size 8",
        ">=1 unsaturated non-aromatic heteroatom-containing ring size 8", ">=2 any ring size 8",
        ">=2 saturated/aromatic carbon-only ring size 8", ">=2 saturated/aromatic nitrogen-containing ring size 8",
        ">=2 saturated/aromatic heteroatom-containing ring size 8", ">=2 unsaturated non-aromatic carbon-only ring size 8",
        ">=2 unsaturated non-aromatic nitrogen-containing ring size 8", ">=2 unsaturated non-aromatic heteroatom-containing ring size 8",

        # Ring size 9 (241-247)
        ">=1 any ring size 9", ">=1 saturated/aromatic carbon-only ring size 9",
        ">=1 saturated/aromatic nitrogen-containing ring size 9", ">=1 saturated/aromatic heteroatom-containing ring size 9",
        ">=1 unsaturated non-aromatic carbon-only ring size 9", ">=1 unsaturated non-aromatic nitrogen-containing ring size 9",
        ">=1 unsaturated non-aromatic heteroatom-containing ring size 9",

        # Ring size 10 (248-254)
        ">=1 any ring size 10", ">=1 saturated/aromatic carbon-only ring size 10",
        ">=1 saturated/aromatic nitrogen-containing ring size 10", ">=1 saturated/aromatic heteroatom-containing ring size 10",
        ">=1 unsaturated non-aromatic carbon-only ring size 10", ">=1 unsaturated non-aromatic nitrogen-containing ring size 10",
        ">=1 unsaturated non-aromatic heteroatom-containing ring size 10",

        # Aromatic rings (255-262)
        ">=1 aromatic ring", ">=1 hetero-aromatic ring", ">=2 aromatic rings", ">=2 hetero-aromatic rings",
        ">=3 aromatic rings", ">=3 hetero-aromatic rings", ">=4 aromatic rings", ">=4 hetero-aromatic rings"
    ]
    pubchem_features.extend(ring_features)

    # Section 3: Simple atom pairs (Bits 263-326)
    atom_pairs = [
        "Li-H", "Li-Li", "Li-B", "Li-C", "Li-O", "Li-F", "Li-P", "Li-S", "Li-Cl",  # 263-271
        "B-H", "B-B", "B-C", "B-N", "B-O", "B-F", "B-Si", "B-P", "B-S", "B-Cl", "B-Br",  # 272-282
        "C-H", "C-C", "C-N", "C-O", "C-F", "C-Na", "C-Mg", "C-Al", "C-Si", "C-P", "C-S", "C-Cl", "C-As", "C-Se", "C-Br", "C-I",  # 283-298
        "N-H", "N-N", "N-O", "N-F", "N-Si", "N-P", "N-S", "N-Cl", "N-Br",  # 299-307
        "O-H", "O-O", "O-Mg", "O-Na", "O-Al", "O-Si", "O-P", "O-K",  # 308-315
        "F-P", "F-S",  # 316-317
        "Al-H", "Al-Cl",  # 318-319
        "Si-H", "Si-Si", "Si-Cl",  # 320-322
        "P-H", "P-P",  # 323-324
        "As-H", "As-As"  # 325-326
    ]
    pubchem_features.extend(atom_pairs)

    # Section 4: Simple atom nearest neighbors (Bits 327-415)
    neighbor_patterns = [
        "C(~Br)(~C)", "C(~Br)(~C)(~C)", "C(~Br)(~H)", "C(~Br)(:C)", "C(~Br)(:N)",  # 327-331
        "C(~C)(~C)", "C(~C)(~C)(~C)", "C(~C)(~C)(~C)(~C)", "C(~C)(~C)(~C)(~H)", "C(~C)(~C)(~C)(~N)",  # 332-336
        "C(~C)(~C)(~C)(~O)", "C(~C)(~C)(~H)(~N)", "C(~C)(~C)(~H)(~O)", "C(~C)(~C)(~N)", "C(~C)(~C)(~O)",  # 337-341
        "C(~C)(~Cl)", "C(~C)(~Cl)(~H)", "C(~C)(~H)", "C(~C)(~H)(~N)", "C(~C)(~H)(~O)",  # 342-346
        "C(~C)(~H)(~O)(~O)", "C(~C)(~H)(~P)", "C(~C)(~H)(~S)", "C(~C)(~I)", "C(~C)(~N)",  # 347-351
        "C(~C)(~O)", "C(~C)(~S)", "C(~C)(~Si)", "C(~C)(:C)", "C(~C)(:C)(:C)",  # 352-356
        "C(~C)(:C)(:N)", "C(~C)(:N)", "C(~C)(:N)(:N)", "C(~Cl)(~Cl)", "C(~Cl)(~H)",  # 357-361
        "C(~Cl)(:C)", "C(~F)(~F)", "C(~F)(:C)", "C(~H)(~N)", "C(~H)(~O)",  # 362-366
        "C(~H)(~O)(~O)", "C(~H)(~S)", "C(~H)(~Si)", "C(~H)(:C)", "C(~H)(:C)(:C)",  # 367-371
        "C(~H)(:C)(:N)", "C(~H)(:N)", "C(~H)(~H)(~H)", "C(~N)(~N)", "C(~N)(:C)",  # 372-376
        "C(~N)(:C)(:C)", "C(~N)(:C)(:N)", "C(~N)(:N)", "C(~O)(~O)", "C(~O)(:C)",  # 377-381
        "C(~O)(:C)(:C)", "C(~S)(:C)", "C(:C)(:C)", "C(:C)(:C)(:C)", "C(:C)(:C)(:N)",  # 382-386
        "C(:C)(:N)", "C(:C)(:N)(:N)", "C(:N)(:N)",  # 387-389
        "N(~C)(~C)", "N(~C)(~C)(~C)", "N(~C)(~C)(~H)", "N(~C)(~H)", "N(~C)(~H)(~N)",  # 390-394
        "N(~C)(~O)", "N(~C)(:C)", "N(~C)(:C)(:C)", "N(~H)(~N)", "N(~H)(:C)",  # 395-399
        "N(~H)(:C)(:C)", "N(~O)(~O)", "N(~O)(:O)", "N(:C)(:C)", "N(:C)(:C)(:C)",  # 400-404
        "O(~C)(~C)", "O(~C)(~H)", "O(~C)(~P)", "O(~H)(~S)", "O(:C)(:C)",  # 405-409
        "P(~C)(~C)", "P(~O)(~O)", "S(~C)(~C)", "S(~C)(~H)", "S(~C)(~O)", "Si(~C)(~C)"  # 410-415
    ]
    pubchem_features.extend(neighbor_patterns)

    # Section 5: Detailed atom neighborhoods (Bits 416-459)
    bond_patterns = [
        "C=C", "C#C", "C=N", "C#N", "C=O", "C=S", "N=N", "N=O", "N=P", "P=O", "P=P",  # 416-426
        "C(#C)(-C)", "C(#C)(-H)", "C(#N)(-C)", "C(-C)(-C)(=C)", "C(-C)(-C)(=N)",  # 427-431
        "C(-C)(-C)(=O)", "C(-C)(-Cl)(=O)", "C(-C)(-H)(=C)", "C(-C)(-H)(=N)", "C(-C)(-H)(=O)",  # 432-436
        "C(-C)(-N)(=C)", "C(-C)(-N)(=N)", "C(-C)(-N)(=O)", "C(-C)(-O)(=O)", "C(-C)(=C)",  # 437-441
        "C(-C)(=N)", "C(-C)(=O)", "C(-Cl)(=O)", "C(-H)(-N)(=C)", "C(-H)(=C)",  # 442-446
        "C(-H)(=N)", "C(-H)(=O)", "C(-N)(=C)", "C(-N)(=N)", "C(-N)(=O)",  # 447-451
        "C(-O)(=O)", "N(-C)(=C)", "N(-C)(=O)", "N(-O)(=O)", "P(-O)(=O)",  # 452-456
        "S(-C)(=O)", "S(-O)(=O)", "S(=O)(=O)"  # 457-459
    ]
    pubchem_features.extend(bond_patterns)

    # Section 6: Simple SMARTS patterns (Bits 460-712)
    smarts_patterns = [
        "C-C-C#C", "O-C-C=N", "O-C-C=O", "N:C-S-[#1]", "N-C-C=C", "O=S-C-C", "N#C-C=C", "C=N-N-C",  # 460-467
        "O=S-C-N", "S-S-C:C", "C:C-C=C", "S:C:C:C", "C:N:C-C", "S-C:N:C", "S:C:C:N", "S-C=N-C",  # 468-475
        "C-O-C=C", "N-N-C:C", "S-C=N-[#1]", "S-C-S-C", "C:S:C-C", "O-S-C:C", "C:N-C:C", "N-S-C:C",  # 476-483
        "N-C:N:C", "N:C:C:N", "N-C:N:N", "N-C=N-C", "N-C=N-[#1]", "N-C-S-C", "C-C-C=C", "C-N:C-[#1]",  # 484-491
        "N-C:O:C", "O=C-C:C", "O=C-C:N", "C-N-C:C", "N:N-C-[#1]", "O-C:C:N", "O-C=C-C", "N-C:C:N",  # 492-499
        "C-S-C:C", "Cl-C:C-C", "N-C=C-[#1]", "Cl-C:C-[#1]", "N:C:N-C", "Cl-C:C-O", "C-C:N:C", "C-C-S-C",  # 500-507
        "S=C-N-C", "Br-C:C-C", "[#1]-N-N-[#1]", "S=C-N-[#1]", "C-[As]-O-[#1]", "S:C:C-[#1]", "O-N-C-C", "N-N-C-C",  # 508-515
        "[#1]-C=C-[#1]", "N-N-C-N", "O=C-N-N", "N=C-N-C", "C=C-C:C", "C:N-C-[#1]", "C-N-N-[#1]", "N:C:C-C",  # 516-523
        "C-C=C-C", "[As]-C:C-[#1]", "Cl-C:C-Cl", "C:C:N-[#1]", "[#1]-N-C-[#1]", "Cl-C-C-Cl", "N:C-C:C", "S-C:C-C",  # 524-531
        "S-C:C-[#1]", "S-C:C-N", "S-C:C-O", "O=C-C-C", "O=C-C-N", "O=C-C-O", "N=C-C-C", "N=C-C-[#1]",  # 532-539
        "C-N-C-[#1]", "O-C:C-C", "O-C:C-[#1]", "O-C:C-N", "O-C:C-O", "N-C:C-C", "N-C:C-[#1]", "N-C:C-N",  # 540-547
        "O-C-C:C", "N-C-C:C", "Cl-C-C-C", "Cl-C-C-O", "C:C-C:C", "O=C-C=C", "Br-C-C-C", "N=C-C=C",  # 548-555
        "C=C-C-C", "N:C-O-[#1]", "O=N-C:C", "O-C-N-[#1]", "N-C-N-C", "Cl-C-C=O", "Br-C-C=O", "O-C-O-C",  # 556-563
        "C=C-C=C", "C:C-O-C", "O-C-C-N", "O-C-C-O", "N#C-C-C", "N-C-C-N", "C:C-C-C", "[#1]-C-O-[#1]",  # 564-571
        "N:C:N:C", "O-C-C=C", "O-C-C:C-C", "O-C-C:C-O", "N=C-C:C-[#1]", "C:C-N-C:C", "C-C:C-C:C", "O=C-C-C-C",  # 572-579
        "O=C-C-C-N", "O=C-C-C-O", "C-C-C-C-C", "Cl-C:C-O-C", "C:C-C=C-C", "C-C:C-N-C", "C-S-C-C-C", "N-C:C-O-[#1]",  # 580-587
        "O=C-C-C=O", "C-C:C-O-C", "C-C:C-O-[#1]", "Cl-C-C-C-C", "N-C-C-C-C", "N-C-C-C-N", "C-O-C-C=C", "C:C-C-C-C",  # 588-595
        "N=C-N-C-C", "O=C-C-C:C", "Cl-C:C:C-C", "[#1]-C-C=C-[#1]", "N-C:C:C-C", "N-C:C:C-N", "O=C-C-N-C", "C-C:C:C-C",  # 596-603
        "C-O-C-C:C", "O=C-C-O-C", "O-C:C-C-C", "N-C-C-C:C", "C-C-C-C:C", "Cl-C-C-N-C", "C-O-C-O-C", "N-C-C-N-C",  # 604-611
        "N-C-O-C-C", "C-N-C-C-C", "C-C-O-C-C", "N-C-C-O-C", "C:C:N:N:C", "C-C-C-O-[#1]", "C:C-C-C:C", "O-C-C=C-C",  # 612-619
        "C:C-O-C-C", "N-C:C:C:N", "O=C-O-C:C", "O=C-C:C-C", "O=C-C:C-N", "O=C-C:C-O", "C-O-C:C-C", "O=[As]-C:C:C",  # 620-627
        "C-N-C-C:C", "S-C:C:C-N", "O-C:C-O-C", "O-C:C-O-[#1]", "C-C-O-C:C", "N-C-C:C-C", "C-C-C:C-C", "N-N-C-N-[#1]",  # 628-635
        "C-N-C-N-C", "O-C-C-C-C", "O-C-C-C-N", "O-C-C-C-O", "C=C-C-C-C", "O-C-C-C=C", "O-C-C-C=O", "[#1]-C-C-N-[#1]",  # 636-643
        "C-C=N-N-C", "O=C-N-C-C", "O=C-N-C-[#1]", "O=C-N-C-N", "O=N-C:C-N", "O=N-C:C-O", "O=C-N-C=O", "O-C:C:C-C",  # 644-651
        "O-C:C:C-N", "O-C:C:C-O", "N-C-N-C-C", "O-C-C-C:C", "C-C-N-C-C", "C-N-C:C-C", "C-C-S-C-C", "O-C-C-N-C",  # 652-659
        "C-C=C-C-C", "O-C-O-C-C", "O-C-C-O-C", "O-C-C-O-[#1]", "C-C=C-C=C", "N-C:C-C-C", "C=C-C-O-C", "C=C-C-O-[#1]",  # 660-667
        "C-C:C-C-C", "Cl-C:C-C=O", "Br-C:C:C-C", "O=C-C=C-C", "O=C-C=C-[#1]", "O=C-C=C-N", "N-C-N-C:C", "Br-C-C-C:C",  # 668-675
        "N#C-C-C-C", "C-C=C-C:C", "C-C-C=C-C", "C-C-C-C-C-C", "O-C-C-C-C-C", "O-C-C-C-C-O", "O-C-C-C-C-N", "N-C-C-C-C-C",  # 676-683
        "O=C-C-C-C-C", "O=C-C-C-C-N", "O=C-C-C-C-O", "O=C-C-C-C=O", "C-C-C-C-C-C-C", "O-C-C-C-C-C-C", "O-C-C-C-C-C-O", "O-C-C-C-C-C-N",  # 684-691
        "O=C-C-C-C-C-C", "O=C-C-C-C-C-O", "O=C-C-C-C-C=O", "O=C-C-C-C-C-N", "C-C-C-C-C-C-C-C", "C-C-C-C-C-C(C)-C", "O-C-C-C-C-C-C-C", "O-C-C-C-C-C(C)-C",  # 692-699
        "O-C-C-C-C-C-O-C", "O-C-C-C-C-C(O)-C", "O-C-C-C-C-C-N-C", "O-C-C-C-C-C(N)-C", "O=C-C-C-C-C-C-C", "O=C-C-C-C-C(O)-C", "O=C-C-C-C-C(=O)-C", "O=C-C-C-C-C(N)-C",  # 700-707
        "C-C(C)-C-C", "C-C(C)-C-C-C", "C-C-C(C)-C-C", "C-C(C)(C)-C-C", "C-C(C)-C(C)-C"  # 708-712
    ]
    pubchem_features.extend(smarts_patterns)

    # Section 7: Complex SMARTS patterns (Bits 713-880)
    complex_smarts = [
        # Aromatic substitution patterns (713-775)
        "Cc1ccc(C)cc1", "Cc1ccc(O)cc1", "Cc1ccc(S)cc1", "Cc1ccc(N)cc1", "Cc1ccc(Cl)cc1", "Cc1ccc(Br)cc1",  # 713-718
        "Oc1ccc(O)cc1", "Oc1ccc(S)cc1", "Oc1ccc(N)cc1", "Oc1ccc(Cl)cc1", "Oc1ccc(Br)cc1",  # 719-723
        "Sc1ccc(S)cc1", "Sc1ccc(N)cc1", "Sc1ccc(Cl)cc1", "Sc1ccc(Br)cc1",  # 724-727
        "Nc1ccc(N)cc1", "Nc1ccc(Cl)cc1", "Nc1ccc(Br)cc1",  # 728-730
        "Clc1ccc(Cl)cc1", "Clc1ccc(Br)cc1", "Brc1ccc(Br)cc1",  # 731-733

        # Meta substitution patterns (734-754)
        "Cc1cc(C)ccc1", "Cc1cc(O)ccc1", "Cc1cc(S)ccc1", "Cc1cc(N)ccc1", "Cc1cc(Cl)ccc1", "Cc1cc(Br)ccc1",  # 734-739
        "Oc1cc(O)ccc1", "Oc1cc(S)ccc1", "Oc1cc(N)ccc1", "Oc1cc(Cl)ccc1", "Oc1cc(Br)ccc1",  # 740-744
        "Sc1cc(S)ccc1", "Sc1cc(N)ccc1", "Sc1cc(Cl)ccc1", "Sc1cc(Br)ccc1",  # 745-748
        "Nc1cc(N)ccc1", "Nc1cc(Cl)ccc1", "Nc1cc(Br)ccc1",  # 749-751
        "Clc1cc(Cl)ccc1", "Clc1cc(Br)ccc1", "Brc1cc(Br)ccc1",  # 752-754

        # Ortho substitution patterns (755-775)
        "Cc1c(C)cccc1", "Cc1c(O)cccc1", "Cc1c(S)cccc1", "Cc1c(N)cccc1", "Cc1c(Cl)cccc1", "Cc1c(Br)cccc1",  # 755-760
        "Oc1c(O)cccc1", "Oc1c(S)cccc1", "Oc1c(N)cccc1", "Oc1c(Cl)cccc1", "Oc1c(Br)cccc1",  # 761-765
        "Sc1c(S)cccc1", "Sc1c(N)cccc1", "Sc1c(Cl)cccc1", "Sc1c(Br)cccc1",  # 766-769
        "Nc1c(N)cccc1", "Nc1c(Cl)cccc1", "Nc1c(Br)cccc1",  # 770-772
        "Clc1c(Cl)cccc1", "Clc1c(Br)cccc1", "Brc1c(Br)cccc1",  # 773-775

        # Cyclohexane substitution patterns (776-880)
        "CC1CCC(C)CC1", "CC1CCC(O)CC1", "CC1CCC(S)CC1", "CC1CCC(N)CC1", "CC1CCC(Cl)CC1", "CC1CCC(Br)CC1",  # 776-781
        "OC1CCC(O)CC1", "OC1CCC(S)CC1", "OC1CCC(N)CC1", "OC1CCC(Cl)CC1", "OC1CCC(Br)CC1",  # 782-786
        "SC1CCC(S)CC1", "SC1CCC(N)CC1", "SC1CCC(Cl)CC1", "SC1CCC(Br)CC1",  # 787-790
        "NC1CCC(N)CC1", "NC1CCC(Cl)CC1", "NC1CCC(Br)CC1",  # 791-793
        "ClC1CCC(Cl)CC1", "ClC1CCC(Br)CC1", "BrC1CCC(Br)CC1",  # 794-796

        "CC1CC(C)CCC1", "CC1CC(O)CCC1", "CC1CC(S)CCC1", "CC1CC(N)CCC1", "CC1CC(Cl)CCC1", "CC1CC(Br)CCC1",  # 797-802
        "OC1CC(O)CCC1", "OC1CC(S)CCC1", "OC1CC(N)CCC1", "OC1CC(Cl)CCC1", "OC1CC(Br)CCC1",  # 803-807
        "SC1CC(S)CCC1", "SC1CC(N)CCC1", "SC1CC(Cl)CCC1", "SC1CC(Br)CCC1",  # 808-811
        "NC1CC(N)CCC1", "NC1CC(Cl)CCC1", "NC1CC(Br)CCC1",  # 812-814
        "ClC1CC(Cl)CCC1", "ClC1CC(Br)CCC1", "BrC1CC(Br)CCC1",  # 815-817

        "CC1C(C)CCCC1", "CC1C(O)CCCC1", "CC1C(S)CCCC1", "CC1C(N)CCCC1", "CC1C(Cl)CCCC1", "CC1C(Br)CCCC1",  # 818-823
        "OC1C(O)CCCC1", "OC1C(S)CCCC1", "OC1C(N)CCCC1", "OC1C(Cl)CCCC1", "OC1C(Br)CCCC1",  # 824-828
        "SC1C(S)CCCC1", "SC1C(N)CCCC1", "SC1C(Cl)CCCC1", "SC1C(Br)CCCC1",  # 829-832
        "NC1C(N)CCCC1", "NC1C(Cl)CCCC1", "NC1C(Br)CCCC1",  # 833-835
        "ClC1C(Cl)CCCC1", "ClC1C(Br)CCCC1", "BrC1C(Br)CCCC1",  # 836-838

        "CC1CC(C)CC1", "CC1CC(O)CC1", "CC1CC(S)CC1", "CC1CC(N)CC1", "CC1CC(Cl)CC1", "CC1CC(Br)CC1",  # 839-844
        "OC1CC(O)CC1", "OC1CC(S)CC1", "OC1CC(N)CC1", "OC1CC(Cl)CC1", "OC1CC(Br)CC1",  # 845-849
        "SC1CC(S)CC1", "SC1CC(N)CC1", "SC1CC(Cl)CC1", "SC1CC(Br)CC1",  # 850-853
        "NC1CC(N)CC1", "NC1CC(Cl)CC1", "NC1CC(Br)CC1",  # 854-856
        "ClC1CC(Cl)CC1", "ClC1CC(Br)CC1", "BrC1CC(Br)CC1",  # 857-859

        "CC1C(C)CCC1", "CC1C(O)CCC1", "CC1C(S)CCC1", "CC1C(N)CCC1", "CC1C(Cl)CCC1", "CC1C(Br)CCC1",  # 860-865
        "OC1C(O)CCC1", "OC1C(S)CCC1", "OC1C(N)CCC1", "OC1C(Cl)CCC1", "OC1C(Br)CCC1",  # 866-870
        "SC1C(S)CCC1", "SC1C(N)CCC1", "SC1C(Cl)CCC1", "SC1C(Br)CCC1",  # 871-874
        "NC1C(N)CCC1", "NC1C(Cl)CC1", "NC1C(Br)CCC1",  # 875-877
        "ClC1C(Cl)CCC1", "ClC1C(Br)CCC1", "BrC1C(Br)CCC1"  # 878-880
    ]
    pubchem_features.extend(complex_smarts)

    # 确保特征数量正确（881位）
    if len(pubchem_features) != 881:
        print(f"警告: PubChem特征数量不匹配，期望881位，实际{len(pubchem_features)}位")
        # 如果不足，补充通用名称
        while len(pubchem_features) < 881:
            idx = len(pubchem_features)
            pubchem_features.append(f"PubChem_bit_{idx}")
        # 如果超出，截断
        pubchem_features = pubchem_features[:881]

    return pubchem_features

def get_feature_names_from_model(n_features):
    """获取PubChem指纹的特征名称"""
    print("获取PubChem指纹特征名称...")
    pubchem_names = get_pubchem_feature_names()
    if pubchem_names:
        # 确保特征数量匹配
        if len(pubchem_names) >= n_features:
            return pubchem_names[:n_features]
        elif len(pubchem_names) < n_features:
            # 如果PubChem特征数量不足，补充通用名称
            extended_names = pubchem_names.copy()
            for i in range(len(pubchem_names), n_features):
                extended_names.append(f"Extra_PubChem_bit_{i}")
            return extended_names
    else:
        print("无法获取PubChem特征名称，使用通用名称")
        return [f"PubChem_bit_{i}" for i in range(n_features)]

def run_shap_explanation(df_with_ad, predictor, args, dataset_id, input_file=None):
    """
    执行 SHAP 解释：
    - 按 scope 过滤（all/ad）
    - 随机种子可复现的降采样
    - 解释器优先 TreeExplainer，失败回退 KernelExplainer
    - 保存数值、图像与元数据
    """
    try:
        import shap
    except Exception as e:
        print(f"未安装 shap 或导入失败: {e}. 可先运行 pip install shap 安装。")
        return

    # 过滤可解释样本
    df_base = df_with_ad.copy()
    if 'error' in df_base.columns:
        # 过滤掉有错误的样本（error列为空字符串或None表示无错误）
        df_base = df_base[(df_base['error'].isna()) | (df_base['error'] == '') | (df_base['error'].isnull())]
    if args.shap_scope == 'ad' and 'in_applicability_domain' in df_base.columns:
        df_base = df_base[df_base['in_applicability_domain'] == True]

    if df_base.empty:
        print("SHAP: 无可解释样本，跳过。")
        return

    smiles_col = 'canonical_smiles' if 'canonical_smiles' in df_base.columns and df_base['canonical_smiles'].notna().any() else 'smiles'
    smiles_list = df_base[smiles_col].astype(str).tolist()
    orig_indices = df_base.index.to_list()

    # 降采样（可复现）
    rng = random.Random(args.shap_seed)
    max_n = args.shap_downsample if args.shap_downsample and args.shap_downsample > 0 else len(smiles_list)
    if len(smiles_list) > max_n:
        idx_selected = sorted(rng.sample(range(len(smiles_list)), max_n))
        smiles_list = [smiles_list[i] for i in idx_selected]
        orig_indices = [orig_indices[i] for i in idx_selected]

    # 构建特征矩阵
    X, _ = build_feature_matrix_from_smiles(predictor, smiles_list)
    if X.size == 0:
        print("SHAP: 未能构建任何特征，跳过。")
        return
    n_features = X.shape[1]
    feature_names = get_feature_names_from_model(n_features)

    # 输出目录
    root = Path(args.shap_output or "shap_analysis")
    ts = datetime.now().strftime("%Y%m%d-%H%M%S")
    safe_ds = "".join([c if c.isalnum() or c in ("-", "_") else "_" for c in (dataset_id or "dataset")])
    out_dir = root / f"{safe_ds}_{ts}"
    out_dir.mkdir(parents=True, exist_ok=True)

    # 选择解释器
    explainer = None
    shap_values = None
    try_tree = args.shap_explainer in ("auto", "tree")
    if try_tree:
        try:
            explainer = shap.TreeExplainer(predictor.model)
            shap_values = explainer.shap_values(X)
        except Exception as e:
            print(f"SHAP: TreeExplainer 失败，回退 KernelExplainer。原因: {e}")

    if shap_values is None:
        # 背景集
        bg_n = min(int(args.shap_background or 100), X.shape[0])
        background = X[:bg_n, :]
        try:
            explainer = shap.KernelExplainer(predictor.model.predict, background)
            shap_values = explainer.shap_values(X, nsamples="auto")
        except Exception as e:
            print(f"SHAP: KernelExplainer 失败: {e}")
            return

    # 绘图：bar图显示正负SHAP值
    try:
        # 生成显示正负SHAP值的条形图
        mean_shap = np.mean(shap_values, axis=0)  # 保留正负号

        # 按绝对值排序，但保留正负号
        sorted_indices = np.argsort(np.abs(mean_shap))[::-1]
        top_n = min(20, len(sorted_indices))  # 显示Top 20特征
        top_indices = sorted_indices[:top_n]

        top_mean_shap = mean_shap[top_indices]
        top_feature_names = [feature_names[i] for i in top_indices]

        # 创建正负条形图
        plt.figure(figsize=(10, 8))
        colors = ['red' if x > 0 else 'blue' for x in top_mean_shap]
        plt.barh(range(len(top_mean_shap)), top_mean_shap, color=colors, alpha=0.7)

        plt.yticks(range(len(top_feature_names)), top_feature_names)
        plt.xlabel('Mean SHAP Value')
        plt.title('Feature Importance with Toxic Effects')
        plt.axvline(x=0, color='black', linestyle='-', alpha=0.3)

        # 添加图例
        from matplotlib.patches import Patch
        legend_elements = [Patch(facecolor='red', alpha=0.7, label='Positive Effect'),
                          Patch(facecolor='blue', alpha=0.7, label='Negative Effect')]
        plt.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(0.98, 0.98))

        plt.gca().invert_yaxis()
        plt.tight_layout()
        plt.savefig(str(out_dir / "bar_plot.png"), dpi=200)
        plt.close()
    except Exception as e:
        print(f"SHAP: 绘制 bar 图失败: {e}")

    # 生成SHAP特征排序摘要
    try:
        # 计算平均绝对SHAP值和平均SHAP值
        mean_abs_shap = np.mean(np.abs(shap_values), axis=0)
        mean_shap = np.mean(shap_values, axis=0)

        # 按绝对值排序
        sorted_indices = np.argsort(-mean_abs_shap)

        # 生成SHAP值排序摘要
        summary_file = out_dir / "shap_feature_ranking.txt"
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("SHAP特征重要性排序摘要\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"分析时间: {ts}\n")
            f.write(f"分析样本数: {shap_values.shape[0]}\n")
            f.write(f"特征总数: {shap_values.shape[1]}\n")
            f.write(f"基线预测值: {explainer.expected_value:.4f}\n\n")

            f.write("特征重要性排序 (按平均绝对SHAP值排序):\n")
            f.write("-" * 80 + "\n")
            f.write(f"{'排名':<4} {'特征名称':<30} {'平均SHAP值':<12} {'平均|SHAP值|':<12} {'效应方向':<10}\n")
            f.write("-" * 80 + "\n")

            for rank, idx in enumerate(sorted_indices, 1):
                feature_name = feature_names[idx]
                mean_shap_val = mean_shap[idx]
                mean_abs_val = mean_abs_shap[idx]
                effect_direction = "正向" if mean_shap_val > 0 else "负向" if mean_shap_val < 0 else "中性"

                f.write(f"{rank:<4} {feature_name:<30} {mean_shap_val:<+12.6f} {mean_abs_val:<12.6f} {effect_direction:<10}\n")

                # 只显示前50个最重要的特征，避免文件过长
                if rank >= 50:
                    f.write(f"... (仅显示前50个最重要特征)\n")
                    break

            f.write("\n" + "=" * 50 + "\n")
            f.write("说明:\n")
            f.write("- 平均SHAP值: 该特征对预测的平均贡献 (正值=增加毒性, 负值=降低毒性)\n")
            f.write("- 平均|SHAP值|: 该特征的平均重要性程度 (绝对值越大越重要)\n")
            f.write("- 效应方向: 该特征的主要作用方向\n")

        print(f"SHAP特征排序摘要已保存到: {summary_file}")

    except Exception as e:
        print(f"SHAP: 生成特征排序摘要失败: {e}")

    print(f"SHAP: 解释完成，输出目录: {out_dir}")

# =============================================================================
# 类似物筛查功能
# =============================================================================

def calculate_molecular_similarity(query_smiles, reference_smiles):
    """计算两个分子的Tanimoto相似性"""
    try:
        query_mol = Chem.MolFromSmiles(query_smiles)
        ref_mol = Chem.MolFromSmiles(reference_smiles)

        if query_mol is None or ref_mol is None:
            return 0.0

        # 生成Morgan指纹
        query_fp = AllChem.GetMorganFingerprintAsBitVect(query_mol, 2, nBits=2048)
        ref_fp = AllChem.GetMorganFingerprintAsBitVect(ref_mol, 2, nBits=2048)

        # 计算Tanimoto相似性
        similarity = DataStructs.TanimotoSimilarity(query_fp, ref_fp)
        return similarity
    except Exception as e:
        print(f"计算相似性失败: {e}")
        return 0.0

def visualize_molecule(smiles, output_dir, filename_prefix="molecule"):
    """
    可视化分子结构并保存为图片

    Args:
        smiles: 分子的SMILES字符串
        output_dir: 输出目录
        filename_prefix: 文件名前缀

    Returns:
        保存的图片文件路径，如果失败则返回None
    """
    try:
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            print(f"无法解析SMILES: {smiles}")
            return None

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 清理SMILES字符串作为文件名（移除特殊字符）
        safe_smiles = "".join(c if c.isalnum() or c in ('-', '_') else '_' for c in smiles)
        # 限制文件名长度
        if len(safe_smiles) > 50:
            safe_smiles = safe_smiles[:50]

        filename = f"{filename_prefix}_{safe_smiles}.png"
        filepath = os.path.join(output_dir, filename)

        # 生成分子图像
        img = Draw.MolToImage(mol, size=(400, 400))
        img.save(filepath)

        print(f"  分子结构已保存: {filepath}")
        return filepath

    except Exception as e:
        print(f"分子可视化失败 {smiles}: {e}")
        return None

def find_similar_compounds(query_data, training_data_path, similarity_threshold=0.3):
    """
    在训练集中寻找类似物

    Args:
        query_data: 查询数据，可以是SMILES列表或包含SMILES和source的DataFrame
        training_data_path: 训练数据路径
        similarity_threshold: 相似性阈值 (默认0.3即30%)

    Returns:
        dict: 每个查询分子的类似物信息
    """
    print(f"\n步骤4: 类似物筛查分析...")
    print(f"相似性阈值: {similarity_threshold:.1%}")

    # 加载训练数据
    try:
        df_train = pd.read_excel(training_data_path)
        if 'smiles' not in df_train.columns:
            print("训练数据中缺少smiles列")
            return {}

        train_smiles = df_train['smiles'].dropna().tolist()
        print(f"训练集化合物数量: {len(train_smiles)}")

    except Exception as e:
        print(f"加载训练数据失败: {e}")
        return {}

    # 处理输入数据格式
    if isinstance(query_data, list):
        # 如果是SMILES列表，转换为DataFrame
        query_df = pd.DataFrame({'smiles': query_data})
    elif isinstance(query_data, pd.DataFrame):
        # 如果是DataFrame，直接使用
        query_df = query_data.copy()
    else:
        print("错误：query_data必须是SMILES列表或DataFrame")
        return {}

    results = {}

    for i, row in query_df.iterrows():
        query_smiles = row['smiles']
        query_source = row.get('source', 'Unknown')  # 获取source信息，如果没有则为Unknown

        print(f"分析化合物 {i+1}/{len(query_df)}: {query_smiles[:50]}...")

        similarities = []

        # 计算与训练集中每个化合物的相似性
        for j, train_smiles in enumerate(train_smiles):
            similarity = calculate_molecular_similarity(query_smiles, train_smiles)
            if similarity >= similarity_threshold:
                # 获取对应的y值和source
                y_value = df_train.iloc[j]['y'] if 'y' in df_train.columns else None
                train_source = df_train.iloc[j]['source'] if 'source' in df_train.columns else 'Unknown'
                similarities.append({
                    'train_smiles': train_smiles,
                    'similarity': similarity,
                    'y_value': y_value,
                    'train_source': train_source,
                    'train_index': j
                })

        # 按相似性排序
        similarities.sort(key=lambda x: x['similarity'], reverse=True)

        # 统计结果
        total_similar = len(similarities)
        top5_similar = similarities[:5]

        results[query_smiles] = {
            'total_count': total_similar,
            'top5_similar': top5_similar,
            'threshold': similarity_threshold,
            'source': query_source  # 添加source信息
        }

        # 输出结果
        print(f"  找到 {total_similar} 个相似化合物 (相似性 ≥ {similarity_threshold:.1%})")
        if top5_similar:
            print(f"  相似性最高的5个化合物:")
            for k, sim_info in enumerate(top5_similar, 1):
                y_str = f", y={sim_info['y_value']:.4f}" if sim_info['y_value'] is not None else ""
                source_str = f", source={sim_info.get('train_source', 'Unknown')}"
                print(f"    {k}. 相似性: {sim_info['similarity']:.3f} ({sim_info['similarity']:.1%}){y_str}{source_str}")
                print(f"       SMILES: {sim_info['train_smiles']}")

                # 可视化类似物分子结构
                visualize_molecule(
                    sim_info['train_smiles'],
                    "result/similar_molecules",
                    f"similar_{i}_{k}"
                )
        else:
            print(f"  未找到相似性 ≥ {similarity_threshold:.1%} 的化合物")
        print()

    return results

def save_similarity_analysis(results, output_dir):
    """保存类似物分析结果到文件"""
    try:
        output_dir = Path(output_dir)
        output_dir.mkdir(exist_ok=True)

        similarity_file = output_dir / "similarity_analysis.txt"

        with open(similarity_file, 'w', encoding='utf-8') as f:
            f.write("类似物筛查分析结果\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"查询化合物数量: {len(results)}\n")
            f.write(f"查询化合物结构图保存位置: result/query_molecules/\n")
            f.write(f"类似物结构图保存位置: result/similar_molecules/\n\n")

            for i, (query_smiles, result) in enumerate(results.items(), 1):
                f.write(f"查询化合物 {i}:\n")
                f.write(f"SMILES: {query_smiles}\n")
                f.write(f"Source: {result.get('source', 'Unknown')}\n")
                f.write(f"相似性阈值: {result['threshold']:.1%}\n")
                f.write(f"找到类似物数量: {result['total_count']}\n\n")

                if result['top5_similar']:
                    f.write("相似性最高的5个化合物:\n")
                    f.write("-" * 120 + "\n")
                    f.write(f"{'排名':<4} {'相似性':<10} {'y值':<12} {'训练集SMILES':<50} {'Source':<40}\n")
                    f.write("-" * 120 + "\n")

                    for j, sim_info in enumerate(result['top5_similar'], 1):
                        y_str = f"{sim_info['y_value']:.4f}" if sim_info['y_value'] is not None else "N/A"
                        source_str = sim_info.get('train_source', 'Unknown')
                        f.write(f"{j:<4} {sim_info['similarity']:.3f}      {y_str:<12} {sim_info['train_smiles']:<50} {source_str:<40}\n")
                else:
                    f.write("未找到符合阈值的类似物\n")

                f.write("\n" + "="*50 + "\n\n")

        print(f"类似物分析结果已保存到: {similarity_file}")

    except Exception as e:
        print(f"保存类似物分析结果失败: {e}")



def main():
    """主函数"""
    # 参数
    parser = argparse.ArgumentParser(description="LightGBM预测与应用域分析（内置 SHAP 和类似物筛查）")
    parser.add_argument("--input-file", type=str, default=None, help="包含 smiles 列的 Excel 文件路径")
    parser.add_argument("--shap", action="store_true", default=True, help="启用 SHAP 解释与可视化（默认启用）")
    parser.add_argument("--shap-scope", choices=["all", "ad"], default="ad", help="解释样本范围")
    parser.add_argument("--shap-downsample", type=int, default=200, help="解释样本最大数量（降采样）")
    parser.add_argument("--shap-explainer", choices=["auto", "tree", "kernel"], default="auto", help="解释器类型")
    parser.add_argument("--shap-topk", type=int, default=10, help="dependence 图的特征数")
    parser.add_argument("--dataset-id", type=str, default=None, help="数据集标识（默认取输入文件名）")
    parser.add_argument("--shap-output", type=str, default="shap_analysis", help="输出根目录")
    parser.add_argument("--shap-seed", type=int, default=42, help="随机种子")
    parser.add_argument("--shap-background", type=int, default=100, help="KernelExplainer 背景样本数")
    args = parser.parse_args()

    # 要预测的SMILES列表
    test_smiles = [
        "CCO",
    ]
    if args.input_file:
        try:
            df_in = pd.read_excel(args.input_file)
            if 'smiles' in df_in.columns:
                test_smiles = df_in['smiles'].dropna().astype(str).tolist()
            else:
                print(f"输入文件缺少 smiles 列，使用默认示例。")
        except Exception as e:
            print(f"读取输入文件失败：{e}，使用默认示例。")

    print("========== LightGBM模型预测与应用域分析 ==========")
    print(f"总共预测 {len(test_smiles)} 个化合物")
    print()

    # 创建预测器实例
    predictor = LightGBMPredictor(MODEL_PATH)

    # 批量预测
    try:
        print("步骤1: 进行LightGBM预测...")
        results = predictor.predict_batch(smiles_list=test_smiles)

        # 将结果转换为DataFrame
        results_df = pd.DataFrame(results)

        print("步骤2: 进行应用域分析...")
        # 加载训练数据
        df_train = load_training_data(TRAINING_DATA_PATH)

        # 计算应用域指标
        df_with_metrics = calculate_ad_metrics(df_train, results_df)

        # 应用应用域判断标准
        df_final = apply_ad_criteria(df_with_metrics, OPTIMAL_DENSLB, OPTIMAL_LDUB)

        # 确保输出目录存在
        os.makedirs("result", exist_ok=True)

        # 保存到Excel文件
        output_file = "result/batch_prediction_with_AD_results.xlsx"
        df_final.to_excel(output_file, index=False, engine='openpyxl')

        print("\n======== 预测和应用域分析完成 ========")
        print(f"结果已保存到: {output_file}")

        # SHAP 解释（默认启用）
        if args.shap:
            print("\n步骤3: 进行SHAP可解释性分析...")
            try:
                dsid = args.dataset_id or (Path(args.input_file).stem if args.input_file else "batch")
                run_shap_explanation(df_final, predictor, args, dsid, args.input_file)
            except Exception as e:
                print(f"SHAP分析失败: {e}")
                print("继续显示预测结果...")
                import traceback
                traceback.print_exc()

        # 类似物筛查分析
        try:
            # 获取有效的数据（包含source信息）
            if 'error' in df_final.columns:
                valid_mask = (df_final['error'].isna()) | (df_final['error'] == '') | (df_final['error'].isnull())
                valid_data = df_final[valid_mask][['smiles', 'source']].copy() if 'source' in df_final.columns else df_final[valid_mask][['smiles']].copy()
            else:
                valid_data = df_final[['smiles', 'source']].copy() if 'source' in df_final.columns else df_final[['smiles']].copy()

            if len(valid_data) > 0:
                # 首先为查询化合物生成可视化
                print("生成查询化合物的分子结构图...")
                for idx, row in valid_data.iterrows():
                    visualize_molecule(
                        row['smiles'],
                        "result/query_molecules",
                        f"query_{idx+1}"
                    )

                # 进行类似物筛查
                similarity_results = find_similar_compounds(
                    valid_data,
                    TRAINING_DATA_PATH,
                    similarity_threshold=0.3
                )

                # 保存类似物分析结果
                save_similarity_analysis(similarity_results, "result")
            else:
                print("没有有效的SMILES进行类似物分析")
        except Exception as e:
            print(f"类似物筛查失败: {e}")
            import traceback
            traceback.print_exc()

        print("\n预测结果摘要:")

        # 应用域统计
        ad_inside = len(df_final[df_final['in_applicability_domain'] == True])
        ad_outside = len(df_final[df_final['in_applicability_domain'] == False])

        print(f"总预测化合物数: {len(df_final)}")
        print(f"应用域内化合物: {ad_inside}")
        print(f"应用域外化合物: {ad_outside}")
        print()

        # 显示前5个结果的详细信息
        for i, (_, row) in enumerate(df_final.head(5).iterrows()):
            print(f"化合物 {i+1}: {row['smiles'][:30]}...")
            if 'prediction_log' in row and pd.notna(row['prediction_log']):
                print(f"  负对数预测值: {row['prediction_log']:.4f}")
                print(f"  原数值预测值: {row['prediction']:.6f} mol/L")
            else:
                print(f"  预测值: {row['prediction']:.4f}")
            ad_status = "应用域内" if row['in_applicability_domain'] else "应用域外"
            print(f"  应用域状态: {ad_status}")
            print(f"  相似性密度: {row['ad_density_value']:.4f} (阈值: {OPTIMAL_DENSLB})")
            print(f"  局域不连续性: {row['ad_ld_value']:.4f} (阈值: {OPTIMAL_LDUB})")
            print(f"  判断原因: {row['ad_reason']}")
            print()

    except Exception as e:
        print(f"预测失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
